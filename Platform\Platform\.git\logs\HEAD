0000000000000000000000000000000000000000 d9a70c055f70d9054ddb1c8336c86af1b82beee1 ValentinoWang <<EMAIL>> 1753840368 +0800	commit (initial): Initial commit
d9a70c055f70d9054ddb1c8336c86af1b82beee1 d9a70c055f70d9054ddb1c8336c86af1b82beee1 ValentinoWang <<EMAIL>> 1753840375 +0800	checkout: moving from feature/database to feature/infra
d9a70c055f70d9054ddb1c8336c86af1b82beee1 792cf0472b0fa575d6ce923f7febb43fd6920a56 ValentinoWang <<EMAIL>> 1753840534 +0800	commit: [DELIVERY] 基础设施CI与Docker配置完成
792cf0472b0fa575d6ce923f7febb43fd6920a56 67a40a05b9c21e95f47ae1d94add976a8b777292 ValentinoWang <<EMAIL>> 1753840577 +0800	commit: [DELIVERY] 数据库架构与迁移脚本完成
67a40a05b9c21e95f47ae1d94add976a8b777292 67a40a05b9c21e95f47ae1d94add976a8b777292 ValentinoWang <<EMAIL>> 1753840701 +0800	checkout: moving from feature/infra to feature/frontend
67a40a05b9c21e95f47ae1d94add976a8b777292 67a40a05b9c21e95f47ae1d94add976a8b777292 ValentinoWang <<EMAIL>> 1753840704 +0800	checkout: moving from feature/frontend to feature/auth-system
67a40a05b9c21e95f47ae1d94add976a8b777292 f11eafd3a0b8590dc114774e8b8b4cd677742ac5 ValentinoWang <<EMAIL>> 1753840921 +0800	commit: [DELIVERY] API路由框架完成 - FastAPI路由骨架搭建完成，包含认证、运动员、训练和比赛管理的完整API端点，所有业务逻辑已标记待实现
f11eafd3a0b8590dc114774e8b8b4cd677742ac5 bb5b65c74b421b5a376754a7f158df458a194def ValentinoWang <<EMAIL>> 1753840961 +0800	commit: [DELIVERY] JWT认证系统实现完成
bb5b65c74b421b5a376754a7f158df458a194def c86dc51fec420da644f8a255f59401d15558caa4 ValentinoWang <<EMAIL>> 1753841280 +0800	commit: [DELIVERY] 前端基础界面完成
c86dc51fec420da644f8a255f59401d15558caa4 c86dc51fec420da644f8a255f59401d15558caa4 ValentinoWang <<EMAIL>> 1753841363 +0800	checkout: moving from feature/auth-system to feature/analytics
c86dc51fec420da644f8a255f59401d15558caa4 50b13062050fcf27c8d203425684d8f7007ebdac ValentinoWang <<EMAIL>> 1753842398 +0800	commit: [FINAL DEV] 业务逻辑与单元测试全部实现
50b13062050fcf27c8d203425684d8f7007ebdac 861e97a4a1da20f4377620f439d38fa058d01f26 ValentinoWang <<EMAIL>> 1753842419 +0800	commit: [DELIVERY] 数据分析功能实现完成
861e97a4a1da20f4377620f439d38fa058d01f26 c247717fa068cbcb1ab790ca65b53028111e48a3 ValentinoWang <<EMAIL>> 1753842555 +0800	commit: 🏆 比赛管理模块业务逻辑实现完成
c247717fa068cbcb1ab790ca65b53028111e48a3 c247717fa068cbcb1ab790ca65b53028111e48a3 ValentinoWang <<EMAIL>> 1753842789 +0800	checkout: moving from feature/analytics to feature/security-audit
c247717fa068cbcb1ab790ca65b53028111e48a3 e03bbb41103628595c89dfb71638a6ec2ca7adc1 ValentinoWang <<EMAIL>> 1753843688 +0800	commit: [SECURITY] 安全审计完成，修复所有高危漏洞
e03bbb41103628595c89dfb71638a6ec2ca7adc1 e03bbb41103628595c89dfb71638a6ec2ca7adc1 ValentinoWang <<EMAIL>> 1753856656 +0800	checkout: moving from feature/security-audit to feature/monitoring
e03bbb41103628595c89dfb71638a6ec2ca7adc1 e03bbb41103628595c89dfb71638a6ec2ca7adc1 ValentinoWang <<EMAIL>> 1753856719 +0800	checkout: moving from feature/monitoring to release/v1.0.0
e03bbb41103628595c89dfb71638a6ec2ca7adc1 ba3d92c64cb9957dac0fd03c0fd0e93172a3bf51 ValentinoWang <<EMAIL>> 1753857065 +0800	commit: [OPS] 生产监控系统配置完成
ba3d92c64cb9957dac0fd03c0fd0e93172a3bf51 96fcf7a8d7f71543c962df56713bf29dd48530e7 ValentinoWang <<EMAIL>> 1753857395 +0800	commit: chore: Release v1.0.0 - 田径运动管理系统正式版
96fcf7a8d7f71543c962df56713bf29dd48530e7 96fcf7a8d7f71543c962df56713bf29dd48530e7 ValentinoWang <<EMAIL>> 1753859539 +0800	checkout: moving from release/v1.0.0 to feature/training-materials
96fcf7a8d7f71543c962df56713bf29dd48530e7 a41def28e77d5e24ac835754b8d1b28d4f359503 ValentinoWang <<EMAIL>> 1753860520 +0800	commit: [FINAL] 项目文档与归档完成
a41def28e77d5e24ac835754b8d1b28d4f359503 fc9a144d8207a08c15ecbc5e1ad01800a5845ae3 ValentinoWang <<EMAIL>> 1754415922 +0800	commit: 已完成重构
fc9a144d8207a08c15ecbc5e1ad01800a5845ae3 844711a8a8a6e18ab2dda6dc060276eb90766f5d ValentinoWang <<EMAIL>> 1754418732 +0800	commit: 能运行了
844711a8a8a6e18ab2dda6dc060276eb90766f5d c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754426048 +0800	commit: 能运行了
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754461399 +0800	checkout: moving from feature/training-materials to feature/1.1.0-data-models
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 2c4351e910745c574076f7f14394536920ce48bf ValentinoWang <<EMAIL>> 1754461508 +0800	commit: [DELIVERY] v1.1.0数据库迁移脚本完成
2c4351e910745c574076f7f14394536920ce48bf f274e0c14685e35ef6d419f43ae945221eb62c96 ValentinoWang <<EMAIL>> 1754461690 +0800	commit: [DELIVERY] v1.1.0数据模型定义完成
f274e0c14685e35ef6d419f43ae945221eb62c96 1fa0c74113575e9504847115a9ab1ce75c3e1292 ValentinoWang <<EMAIL>> 1754462005 +0800	commit: [DELIVERY] v1.1.0 API路由框架完成
1fa0c74113575e9504847115a9ab1ce75c3e1292 734567423aacfe961f6a0c5c2511127dae328b48 ValentinoWang <<EMAIL>> 1754462034 +0800	commit: [DELIVERY] v1.1.0服务层框架完成
734567423aacfe961f6a0c5c2511127dae328b48 af8b580f833e356a7fa8c093bbafd376fc88c961 ValentinoWang <<EMAIL>> 1754462072 +0800	commit: [ADDITIONAL] 添加v1.1.0核心服务文件
af8b580f833e356a7fa8c093bbafd376fc88c961 af8b580f833e356a7fa8c093bbafd376fc88c961 ValentinoWang <<EMAIL>> 1754462149 +0800	checkout: moving from feature/1.1.0-data-models to feature/1.1.0-business-logic
af8b580f833e356a7fa8c093bbafd376fc88c961 af8b580f833e356a7fa8c093bbafd376fc88c961 ValentinoWang <<EMAIL>> 1754462718 +0800	checkout: moving from feature/1.1.0-business-logic to feature/1.1.0-frontend
af8b580f833e356a7fa8c093bbafd376fc88c961 74e4dbd6b89b3fa7561f505c1efd1308a7c322f5 ValentinoWang <<EMAIL>> 1754463882 +0800	commit: [DELIVERY] v1.1.0集成测试完成
74e4dbd6b89b3fa7561f505c1efd1308a7c322f5 74e4dbd6b89b3fa7561f505c1efd1308a7c322f5 ValentinoWang <<EMAIL>> 1754468790 +0800	checkout: moving from feature/1.1.0-frontend to feature/1.1.0-documentation
74e4dbd6b89b3fa7561f505c1efd1308a7c322f5 8320f03d0b73cc150e3c0d46d32888e6285d28dd ValentinoWang <<EMAIL>> 1754469254 +0800	commit: [DELIVERY] v1.1.0文档编写完成
8320f03d0b73cc150e3c0d46d32888e6285d28dd 77afe43a9a95ba258da53e8a7abdcc8797f016bc ValentinoWang <<EMAIL>> 1754469980 +0800	commit: Configure Git LFS for database and media files
77afe43a9a95ba258da53e8a7abdcc8797f016bc 3aae04bc78baab3c4149605fea85a467c2f4ceb4 ValentinoWang <<EMAIL>> 1754470020 +0800	commit: Add database files to Git LFS
3aae04bc78baab3c4149605fea85a467c2f4ceb4 96a97808bfc5ced7df5fa093aca50e54efd3c017 ValentinoWang <<EMAIL>> 1754470144 +0800	commit: 1.1.0 发布
96a97808bfc5ced7df5fa093aca50e54efd3c017 c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754470857 +0800	checkout: moving from feature/1.1.0-documentation to feature/1.2.0-models
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754470862 +0800	checkout: moving from feature/1.2.0-models to feature/1.2.0-database
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 c0e044669024eb1ee7a6cf75ad1f337a3eeb2c31 ValentinoWang <<EMAIL>> 1754471028 +0800	commit: feat(database): create v1.2.0 advanced competition tables
c0e044669024eb1ee7a6cf75ad1f337a3eeb2c31 649e3da85298d56e81c3f28c340dc64181437dbc ValentinoWang <<EMAIL>> 1754471214 +0800	commit: feat(models): define advanced competition management models v1.2.0
649e3da85298d56e81c3f28c340dc64181437dbc c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754471228 +0800	checkout: moving from feature/1.2.0-database to feature/1.2.0-models
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 242d26feb2b32fd4c5e8b556e2beb067bee5b371 ValentinoWang <<EMAIL>> 1754471567 +0800	commit: feat(models): define advanced competition management models v1.2.0
242d26feb2b32fd4c5e8b556e2beb067bee5b371 242d26feb2b32fd4c5e8b556e2beb067bee5b371 ValentinoWang <<EMAIL>> 1754471732 +0800	checkout: moving from feature/1.2.0-models to feature/1.2.0-services
242d26feb2b32fd4c5e8b556e2beb067bee5b371 242d26feb2b32fd4c5e8b556e2beb067bee5b371 ValentinoWang <<EMAIL>> 1754471743 +0800	checkout: moving from feature/1.2.0-services to feature/1.2.0-api-routes
242d26feb2b32fd4c5e8b556e2beb067bee5b371 69647d07faa90f947bee4d412449d0fec9b391c2 ValentinoWang <<EMAIL>> 1754471745 +0800	merge feature/1.2.0-database: Merge made by the 'ort' strategy.
69647d07faa90f947bee4d412449d0fec9b391c2 242d26feb2b32fd4c5e8b556e2beb067bee5b371 ValentinoWang <<EMAIL>> 1754472100 +0800	checkout: moving from feature/1.2.0-api-routes to feature/1.2.0-services
242d26feb2b32fd4c5e8b556e2beb067bee5b371 87048b9970e417c236a8c53da74e0bad8177d98f ValentinoWang <<EMAIL>> 1754472108 +0800	commit: feat(services): implement v1.2.0 business services
87048b9970e417c236a8c53da74e0bad8177d98f 573453a06734ed702ea9c0b09368afded384af79 ValentinoWang <<EMAIL>> 1754472198 +0800	commit: feat(api): implement v1.2.0 advanced competition routes
573453a06734ed702ea9c0b09368afded384af79 69647d07faa90f947bee4d412449d0fec9b391c2 ValentinoWang <<EMAIL>> 1754472203 +0800	checkout: moving from feature/1.2.0-services to feature/1.2.0-api-routes
69647d07faa90f947bee4d412449d0fec9b391c2 e1330871cd783c2926a60ae2addf3c39bc141e0a ValentinoWang <<EMAIL>> 1754472244 +0800	cherry-pick: feat(api): implement v1.2.0 advanced competition routes
e1330871cd783c2926a60ae2addf3c39bc141e0a 573453a06734ed702ea9c0b09368afded384af79 ValentinoWang <<EMAIL>> 1754472632 +0800	checkout: moving from feature/1.2.0-api-routes to feature/1.2.0-services
573453a06734ed702ea9c0b09368afded384af79 573453a06734ed702ea9c0b09368afded384af79 ValentinoWang <<EMAIL>> 1754473010 +0800	checkout: moving from feature/1.2.0-services to feature/1.2.0-frontend
573453a06734ed702ea9c0b09368afded384af79 c91f905810e34e24f4c7f178b00419675f150756 ValentinoWang <<EMAIL>> 1754473018 +0800	merge feature/1.2.0-api-routes: Merge made by the 'ort' strategy.
c91f905810e34e24f4c7f178b00419675f150756 c91f905810e34e24f4c7f178b00419675f150756 ValentinoWang <<EMAIL>> 1754473199 +0800	checkout: moving from feature/1.2.0-frontend to feature/1.1.0-deployment
c91f905810e34e24f4c7f178b00419675f150756 c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754474043 +0800	checkout: moving from feature/1.1.0-deployment to feature/training-materials
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754474054 +0800	checkout: moving from feature/training-materials to feature/1.1.0-deployment
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 da0c4f0a224630b8b3699ba20b2a7b97c9f565c3 ValentinoWang <<EMAIL>> 1754474080 +0800	commit: 保存AI协作文档
da0c4f0a224630b8b3699ba20b2a7b97c9f565c3 da0c4f0a224630b8b3699ba20b2a7b97c9f565c3 ValentinoWang <<EMAIL>> 1754474096 +0800	reset: moving to HEAD
da0c4f0a224630b8b3699ba20b2a7b97c9f565c3 1146ca2a111aaf076e5619be456b691ce6244eab ValentinoWang <<EMAIL>> 1754474308 +0800	commit: [DELIVERY] v1.1.0部署配置完成
1146ca2a111aaf076e5619be456b691ce6244eab 1146ca2a111aaf076e5619be456b691ce6244eab ValentinoWang <<EMAIL>> 1754474321 +0800	checkout: moving from feature/1.1.0-deployment to feature/1.1.0-integration
1146ca2a111aaf076e5619be456b691ce6244eab e5ad4d8186593b235d3737a743b6f2501d2792ab ValentinoWang <<EMAIL>> 1754474339 +0800	merge feature/1.1.0-services: Merge made by the 'ort' strategy.
e5ad4d8186593b235d3737a743b6f2501d2792ab 1ffcddc18ff7ff3c7863c3f0f9b97c64e8fea046 ValentinoWang <<EMAIL>> 1754474344 +0800	merge feature/1.1.0-business-logic: Merge made by the 'ort' strategy.
1ffcddc18ff7ff3c7863c3f0f9b97c64e8fea046 d2997a911fd03d26073382ccb8f24cf312ebcf93 ValentinoWang <<EMAIL>> 1754474350 +0800	merge feature/1.1.0-frontend: Merge made by the 'ort' strategy.
d2997a911fd03d26073382ccb8f24cf312ebcf93 d2997a911fd03d26073382ccb8f24cf312ebcf93 ValentinoWang <<EMAIL>> 1754474370 +0800	reset: moving to HEAD
d2997a911fd03d26073382ccb8f24cf312ebcf93 d2997a911fd03d26073382ccb8f24cf312ebcf93 ValentinoWang <<EMAIL>> 1754474490 +0800	checkout: moving from feature/1.1.0-integration to release/v1.1.0
d2997a911fd03d26073382ccb8f24cf312ebcf93 f2d19b094752727ded190d38d2bdceec2807f28c ValentinoWang <<EMAIL>> 1754474565 +0800	commit: Release v1.1.0 - 运动员等级管理增强版
f2d19b094752727ded190d38d2bdceec2807f28c f2d19b094752727ded190d38d2bdceec2807f28c ValentinoWang <<EMAIL>> 1754474697 +0800	checkout: moving from release/v1.1.0 to feature/1.2.0-integration
f2d19b094752727ded190d38d2bdceec2807f28c da95e9d2257d7ab879832aedce3f90eea3d7bf6f ValentinoWang <<EMAIL>> 1754474731 +0800	commit: docs: 记录v1.2.0开发状态
da95e9d2257d7ab879832aedce3f90eea3d7bf6f f2d19b094752727ded190d38d2bdceec2807f28c ValentinoWang <<EMAIL>> 1754474791 +0800	checkout: moving from feature/1.2.0-integration to release/v1.1.0
f2d19b094752727ded190d38d2bdceec2807f28c d2997a911fd03d26073382ccb8f24cf312ebcf93 ValentinoWang <<EMAIL>> 1754474801 +0800	checkout: moving from release/v1.1.0 to develop/v1.2.0
d2997a911fd03d26073382ccb8f24cf312ebcf93 3896410be82fc68afd2f7ab8b26f355fee5ae531 ValentinoWang <<EMAIL>> 1754474893 +0800	commit (merge): Merge feature/1.2.0-models - 合并v1.2.0数据模型
3896410be82fc68afd2f7ab8b26f355fee5ae531 3896410be82fc68afd2f7ab8b26f355fee5ae531 ValentinoWang <<EMAIL>> 1754474906 +0800	reset: moving to HEAD
3896410be82fc68afd2f7ab8b26f355fee5ae531 3896410be82fc68afd2f7ab8b26f355fee5ae531 ValentinoWang <<EMAIL>> 1754474918 +0800	reset: moving to HEAD
3896410be82fc68afd2f7ab8b26f355fee5ae531 d90f3c071c31e062bffc7090ff58712e5faa105a ValentinoWang <<EMAIL>> 1754474958 +0800	commit: docs: v1.2.0合并状态分析
d90f3c071c31e062bffc7090ff58712e5faa105a f2d19b094752727ded190d38d2bdceec2807f28c ValentinoWang <<EMAIL>> 1754474993 +0800	checkout: moving from develop/v1.2.0 to release/v1.1.0
f2d19b094752727ded190d38d2bdceec2807f28c 5e4542f4adf42f103d2b4fbf3649dfc339b10522 ValentinoWang <<EMAIL>> 1754475197 +0800	commit: feat: 完成v1.1.0部署配置
5e4542f4adf42f103d2b4fbf3649dfc339b10522 609a3e7fca6a775989b53be62b9a83a2d709bdf5 ValentinoWang <<EMAIL>> 1754475216 +0800	commit: docs: 添加快速部署指令
609a3e7fca6a775989b53be62b9a83a2d709bdf5 86e1ce8abb102c00244fc97e1682640b8c27ad83 ValentinoWang <<EMAIL>> 1754476792 +0800	commit: ✅ 完成 v1.1.0 部署
86e1ce8abb102c00244fc97e1682640b8c27ad83 d90f3c071c31e062bffc7090ff58712e5faa105a ValentinoWang <<EMAIL>> 1754476830 +0800	checkout: moving from release/v1.1.0 to develop/v1.2.0
d90f3c071c31e062bffc7090ff58712e5faa105a d90f3c071c31e062bffc7090ff58712e5faa105a ValentinoWang <<EMAIL>> 1754477387 +0800	reset: moving to HEAD
d90f3c071c31e062bffc7090ff58712e5faa105a 573453a06734ed702ea9c0b09368afded384af79 ValentinoWang <<EMAIL>> 1754477402 +0800	checkout: moving from develop/v1.2.0 to feature/1.2.0-services
573453a06734ed702ea9c0b09368afded384af79 a70f664d319447fa29ec947fb1e1bbadf2c4385f ValentinoWang <<EMAIL>> 1754477641 +0800	commit: feat(v1.2.0): 完成服务层开发并恢复250806文档
a70f664d319447fa29ec947fb1e1bbadf2c4385f 86e1ce8abb102c00244fc97e1682640b8c27ad83 ValentinoWang <<EMAIL>> 1754477763 +0800	checkout: moving from feature/1.2.0-services to release/v1.1.0
86e1ce8abb102c00244fc97e1682640b8c27ad83 86e1ce8abb102c00244fc97e1682640b8c27ad83 ValentinoWang <<EMAIL>> 1754477822 +0800	checkout: moving from release/v1.1.0 to develop/v1.2.0
86e1ce8abb102c00244fc97e1682640b8c27ad83 d5b44ce7d6ac1770a360a3cf5e969364059f7818 ValentinoWang <<EMAIL>> 1754477890 +0800	commit: feat: 基于稳定v1.1.0初始化v1.2.0开发分支
d5b44ce7d6ac1770a360a3cf5e969364059f7818 d5b44ce7d6ac1770a360a3cf5e969364059f7818 ValentinoWang <<EMAIL>> 1754478143 +0800	checkout: moving from develop/v1.2.0 to feature/1.2.0-models
d5b44ce7d6ac1770a360a3cf5e969364059f7818 c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 ValentinoWang <<EMAIL>> 1754478154 +0800	checkout: moving from feature/1.2.0-models to feature/1.2.0-database
c7a8a3f25db6e7ec0126bdafaa4f79b0212919f6 776cd1785e4ae72a91a4b76882540cc0bc243e7c ValentinoWang <<EMAIL>> 1754480307 +0800	commit: feat(database): create v1.2.0 advanced competition tables
776cd1785e4ae72a91a4b76882540cc0bc243e7c c10153349194eb15baacd3c7d8ffbfd9bcc4b650 ValentinoWang <<EMAIL>> 1754480650 +0800	commit: feat(models): define advanced competition management models v1.2.0
c10153349194eb15baacd3c7d8ffbfd9bcc4b650 86e1ce8abb102c00244fc97e1682640b8c27ad83 ValentinoWang <<EMAIL>> 1754480714 +0800	checkout: moving from feature/1.2.0-database to release/v1.1.0
86e1ce8abb102c00244fc97e1682640b8c27ad83 c10153349194eb15baacd3c7d8ffbfd9bcc4b650 ValentinoWang <<EMAIL>> 1754480730 +0800	checkout: moving from release/v1.1.0 to feature/1.2.0-database
c10153349194eb15baacd3c7d8ffbfd9bcc4b650 c10153349194eb15baacd3c7d8ffbfd9bcc4b650 ValentinoWang <<EMAIL>> 1754480747 +0800	checkout: moving from feature/1.2.0-database to feature/1.2.0-services
c10153349194eb15baacd3c7d8ffbfd9bcc4b650 c10153349194eb15baacd3c7d8ffbfd9bcc4b650 ValentinoWang <<EMAIL>> 1754480747 +0800	checkout: moving from feature/1.2.0-services to feature/1.2.0-api-routes
c10153349194eb15baacd3c7d8ffbfd9bcc4b650 1f7b5557f0c237210fd9246b155a340d93ca9d85 ValentinoWang <<EMAIL>> 1754480937 +0800	commit: feat: merge 250806 folder from release/v1.1.0 branch
1f7b5557f0c237210fd9246b155a340d93ca9d85 005a9284c0243ec364ac0a24c68dcc70abfd5b8e ValentinoWang <<EMAIL>> 1754481258 +0800	commit: feat(api): implement v1.2.0 advanced competition routes
005a9284c0243ec364ac0a24c68dcc70abfd5b8e d785c47c1a60385797c4e3df95bebb2192be2d1d ValentinoWang <<EMAIL>> 1754481280 +0800	commit: feat(services): implement v1.2.0 business services
d785c47c1a60385797c4e3df95bebb2192be2d1d d785c47c1a60385797c4e3df95bebb2192be2d1d ValentinoWang <<EMAIL>> 1754481349 +0800	checkout: moving from feature/1.2.0-api-routes to feature/1.2.0-frontend
d785c47c1a60385797c4e3df95bebb2192be2d1d feedf579a14b35f4f0c151128cd78b465c56b300 ValentinoWang <<EMAIL>> 1754481919 +0800	commit: feat(frontend): implement v1.2.0 competition UI
feedf579a14b35f4f0c151128cd78b465c56b300 feedf579a14b35f4f0c151128cd78b465c56b300 ValentinoWang <<EMAIL>> 1754482032 +0800	checkout: moving from feature/1.2.0-frontend to feature/1.2.0-business-logic
feedf579a14b35f4f0c151128cd78b465c56b300 982e3e9ac4198bff3ec8e83c178b6f825992b78b ValentinoWang <<EMAIL>> 1754482131 +0800	commit (merge): merge: resolve conflicts and integrate v1.1.0 with v1.2.0 models
982e3e9ac4198bff3ec8e83c178b6f825992b78b d66c5cd8f43d037539cc5f954cd5ea999f934e3f ValentinoWang <<EMAIL>> 1754483746 +0800	commit: feat: 实现v1.2.0核心业务逻辑和前端组件
d66c5cd8f43d037539cc5f954cd5ea999f934e3f d66c5cd8f43d037539cc5f954cd5ea999f934e3f ValentinoWang <<EMAIL>> 1754484099 +0800	checkout: moving from feature/1.2.0-business-logic to feature/1.5.0-testing
d66c5cd8f43d037539cc5f954cd5ea999f934e3f d5b44ce7d6ac1770a360a3cf5e969364059f7818 ValentinoWang <<EMAIL>> 1754484175 +0800	checkout: moving from feature/1.5.0-testing to develop/v1.2.0
d5b44ce7d6ac1770a360a3cf5e969364059f7818 d5b44ce7d6ac1770a360a3cf5e969364059f7818 ValentinoWang <<EMAIL>> 1754484183 +0800	checkout: moving from develop/v1.2.0 to feature/1.2.0-testing
d5b44ce7d6ac1770a360a3cf5e969364059f7818 b5c2c7b00260080f74447f71650788ffd5220d8c ValentinoWang <<EMAIL>> 1754484820 +0800	commit: feat: 完成 v1.2.0 高级比赛管理模块完整实现
b5c2c7b00260080f74447f71650788ffd5220d8c fbf88bb9889126783d9f49c0d29aa70047f35114 ValentinoWang <<EMAIL>> 1754484882 +0800	commit: docs: 添加v1.7.3训练数据库模型分析文档
fbf88bb9889126783d9f49c0d29aa70047f35114 fbf88bb9889126783d9f49c0d29aa70047f35114 ValentinoWang <<EMAIL>> 1754502527 +0800	checkout: moving from feature/1.2.0-testing to feature/1.2.0-documentation
fbf88bb9889126783d9f49c0d29aa70047f35114 d6c6b19ab1e7a15517c1f999c79813649735970d ValentinoWang <<EMAIL>> 1754504160 +0800	commit: docs(v1.2.0): comprehensive documentation
d6c6b19ab1e7a15517c1f999c79813649735970d 1b417d14921c09305635f846506279001ccbc8fa ValentinoWang <<EMAIL>> 1754559353 +0800	commit: 很完整的功能，修改了v1.1.0和v1.2.0，请不要丢失这一版本，谢谢。
1b417d14921c09305635f846506279001ccbc8fa c38581ed6ec9ace0669921b5a30518dc915b8c65 ValentinoWang <<EMAIL>> 1754559488 +0800	commit: refactor: reorganize database files to database/ directory
c38581ed6ec9ace0669921b5a30518dc915b8c65 c38581ed6ec9ace0669921b5a30518dc915b8c65 ValentinoWang <<EMAIL>> 1754559747 +0800	checkout: moving from feature/1.2.0-documentation to release/v1.2.0
c38581ed6ec9ace0669921b5a30518dc915b8c65 c38581ed6ec9ace0669921b5a30518dc915b8c65 ValentinoWang <<EMAIL>> 1754559779 +0800	reset: moving to HEAD
c38581ed6ec9ace0669921b5a30518dc915b8c65 faf77533480d3b783f263f9261a2f412fb325685 ValentinoWang <<EMAIL>> 1754559932 +0800	commit: [RELEASE] v1.2.0 Advanced Competition Management
faf77533480d3b783f263f9261a2f412fb325685 bea04608ba16781564943060f67734ced250c2a4 ValentinoWang <<EMAIL>> 1754560084 +0800	commit: docs: add release documentation and finalize version updates
bea04608ba16781564943060f67734ced250c2a4 5657e18863754b1b92bfde33e9b627cadff83fc2 ValentinoWang <<EMAIL>> 1754560615 +0800	commit: 已经清理
5657e18863754b1b92bfde33e9b627cadff83fc2 882a1459ef641a298688f3fc1253f6dca6900de8 ValentinoWang <<EMAIL>> 1754560763 +0800	commit: v1.2.0最终版
882a1459ef641a298688f3fc1253f6dca6900de8 882a1459ef641a298688f3fc1253f6dca6900de8 ValentinoWang <<EMAIL>> 1754560837 +0800	checkout: moving from release/v1.2.0 to develop/v1.3.0
882a1459ef641a298688f3fc1253f6dca6900de8 882a1459ef641a298688f3fc1253f6dca6900de8 ValentinoWang <<EMAIL>> 1754560847 +0800	checkout: moving from develop/v1.3.0 to feature/1.3.0-action-library
882a1459ef641a298688f3fc1253f6dca6900de8 574a19db494f7906e35097bf4a607df95ad31abf ValentinoWang <<EMAIL>> 1754560883 +0800	commit: feat: initialize v1.3.0 action library development
574a19db494f7906e35097bf4a607df95ad31abf 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754570227 +0800	commit: feat: Add comprehensive tests for Daily Plans, Exercise Library, and Metrics Library
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754578142 +0800	checkout: moving from feature/1.3.0-action-library to feature/database-schema
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754578155 +0800	reset: moving to HEAD
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 882a1459ef641a298688f3fc1253f6dca6900de8 ValentinoWang <<EMAIL>> 1754578155 +0800	checkout: moving from feature/database-schema to feature/api-contracts
882a1459ef641a298688f3fc1253f6dca6900de8 882a1459ef641a298688f3fc1253f6dca6900de8 ValentinoWang <<EMAIL>> 1754578646 +0800	reset: moving to HEAD
882a1459ef641a298688f3fc1253f6dca6900de8 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754578722 +0800	checkout: moving from feature/api-contracts to feature/database-schema
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 091ee70183805d21e25e20d1eadf90cd66fe2545 ValentinoWang <<EMAIL>> 1754578874 +0800	commit: feat(db): implement metrics library database schema with 7 core tables
091ee70183805d21e25e20d1eadf90cd66fe2545 882a1459ef641a298688f3fc1253f6dca6900de8 ValentinoWang <<EMAIL>> 1754578921 +0800	checkout: moving from feature/database-schema to feature/api-contracts
882a1459ef641a298688f3fc1253f6dca6900de8 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754579051 +0800	commit: feat(api): define complete API contracts for metrics library
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754579106 +0800	checkout: moving from feature/api-contracts to feature/frontend-ui
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754579113 +0800	checkout: moving from feature/frontend-ui to feature/backend-services
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754579115 +0800	checkout: moving from feature/backend-services to feature/state-management
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 64ca19707a29f96dc5a1b5126061a53e1850d051 ValentinoWang <<EMAIL>> 1754579584 +0800	commit: feat(frontend): implement metrics library UI with three-section layout
64ca19707a29f96dc5a1b5126061a53e1850d051 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754579609 +0800	checkout: moving from feature/state-management to feature/frontend-ui
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 75ddf1899fdf9a265de5773ccc3917d981edea02 ValentinoWang <<EMAIL>> 1754579622 +0800	commit: feat(backend): implement complete metrics library business logic
75ddf1899fdf9a265de5773ccc3917d981edea02 6b7482eabfcaad5ae5054942c31baca6b899bc24 ValentinoWang <<EMAIL>> 1754579755 +0800	commit: feat(frontend): implement metrics library UI with three-section layout
6b7482eabfcaad5ae5054942c31baca6b899bc24 6b7482eabfcaad5ae5054942c31baca6b899bc24 ValentinoWang <<EMAIL>> 1754579810 +0800	reset: moving to HEAD
6b7482eabfcaad5ae5054942c31baca6b899bc24 64ca19707a29f96dc5a1b5126061a53e1850d051 ValentinoWang <<EMAIL>> 1754579810 +0800	checkout: moving from feature/frontend-ui to feature/state-management
64ca19707a29f96dc5a1b5126061a53e1850d051 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580294 +0800	checkout: moving from feature/state-management to feature/1.3.0-action-library
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580442 +0800	checkout: moving from feature/1.3.0-action-library to feature/integration
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580452 +0800	checkout: moving from feature/integration to feature/e2e-tests
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580456 +0800	merge origin/feature/backend-services: updating HEAD
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580458 +0800	merge origin/feature/frontend-ui: updating HEAD
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580464 +0800	reset: moving to HEAD
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 38ba99f2452bc41ee730371702297186cfc29123 ValentinoWang <<EMAIL>> 1754580470 +0800	merge origin/feature/frontend-ui: Merge made by the 'ort' strategy.
38ba99f2452bc41ee730371702297186cfc29123 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580483 +0800	checkout: moving from feature/e2e-tests to feature/integration
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580489 +0800	merge origin/feature/backend-services: updating HEAD
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 96fcf7a8d7f71543c962df56713bf29dd48530e7 ValentinoWang <<EMAIL>> 1754580489 +0800	checkout: moving from feature/integration to feature/documentation
96fcf7a8d7f71543c962df56713bf29dd48530e7 f79641de9b911985dcf85b5cb827481cfee541fa ValentinoWang <<EMAIL>> 1754580502 +0800	commit: chore: save current AI collaboration prompt and store files
f79641de9b911985dcf85b5cb827481cfee541fa 37d9294b6af7cbdf9bdbd03913765af1bd8866b4 ValentinoWang <<EMAIL>> 1754580508 +0800	checkout: moving from feature/documentation to feature/integration
37d9294b6af7cbdf9bdbd03913765af1bd8866b4 85b8f5d8d390e71a65aa2b69f941ea3ee9f8b527 ValentinoWang <<EMAIL>> 1754580514 +0800	merge origin/feature/backend-services: Merge made by the 'ort' strategy.
85b8f5d8d390e71a65aa2b69f941ea3ee9f8b527 59ffedad64a64ce8efc121fab58034c7f41aaa62 ValentinoWang <<EMAIL>> 1754580520 +0800	merge origin/feature/frontend-ui: Merge made by the 'ort' strategy.
59ffedad64a64ce8efc121fab58034c7f41aaa62 c1150564b4185931603b17fce234e28ba96e92d9 ValentinoWang <<EMAIL>> 1754580647 +0800	commit (merge): resolve: merge conflicts between frontend-ui and state-management branches
c1150564b4185931603b17fce234e28ba96e92d9 02500b59ef6881119dc71829b0a7c2fdc3b1b4cc ValentinoWang <<EMAIL>> 1754581282 +0800	commit: docs: add comprehensive documentation for metrics library v1.3.0
02500b59ef6881119dc71829b0a7c2fdc3b1b4cc f1a6861a1cd296493ccaad9696df1323d21b68de ValentinoWang <<EMAIL>> 1754581370 +0800	commit: test(e2e): add comprehensive Playwright tests for metrics library
f1a6861a1cd296493ccaad9696df1323d21b68de bc8119eab534e97d48221f621190236b7ea7f0e3 ValentinoWang <<EMAIL>> 1754581395 +0800	commit: feat: v1.3.0 集成测试完成 - 指标库管理系统
bc8119eab534e97d48221f621190236b7ea7f0e3 430ba59830079c7941caaa4a9a4e2658f4eb4430 ValentinoWang <<EMAIL>> 1754581826 +0800	commit (merge): feat: 合并database-schema分支 - 完善数据库架构
430ba59830079c7941caaa4a9a4e2658f4eb4430 bc8119eab534e97d48221f621190236b7ea7f0e3 ValentinoWang <<EMAIL>> 1754582485 +0800	reset: moving to bc8119e
bc8119eab534e97d48221f621190236b7ea7f0e3 e1a76ad78703c3f176a1e16d4ff5e8c84081cb4a ValentinoWang <<EMAIL>> 1754582543 +0800	commit: test(e2e): add additional E2E test suites for performance and responsive design
e1a76ad78703c3f176a1e16d4ff5e8c84081cb4a e1a76ad78703c3f176a1e16d4ff5e8c84081cb4a ValentinoWang <<EMAIL>> 1754582609 +0800	checkout: moving from feature/integration to feature/performance
e1a76ad78703c3f176a1e16d4ff5e8c84081cb4a b86829becc4363ea401f67c020ccccc8d74f6de4 ValentinoWang <<EMAIL>> 1754582966 +0800	commit: perf: implement comprehensive performance optimizations
b86829becc4363ea401f67c020ccccc8d74f6de4 b86829becc4363ea401f67c020ccccc8d74f6de4 ValentinoWang <<EMAIL>> 1754582971 +0800	checkout: moving from feature/performance to feature/devops
b86829becc4363ea401f67c020ccccc8d74f6de4 63a08577965bc30fb492c2b7b80a45c7f101dcfc ValentinoWang <<EMAIL>> 1754583266 +0800	commit: feat(devops): implement complete DevOps configuration for production deployment
63a08577965bc30fb492c2b7b80a45c7f101dcfc 63a08577965bc30fb492c2b7b80a45c7f101dcfc ValentinoWang <<EMAIL>> 1754583281 +0800	checkout: moving from feature/devops to release/v1.3.0
63a08577965bc30fb492c2b7b80a45c7f101dcfc 091ee70183805d21e25e20d1eadf90cd66fe2545 ValentinoWang <<EMAIL>> 1754583453 +0800	checkout: moving from release/v1.3.0 to feature/database-schema
091ee70183805d21e25e20d1eadf90cd66fe2545 63a08577965bc30fb492c2b7b80a45c7f101dcfc ValentinoWang <<EMAIL>> 1754583472 +0800	checkout: moving from feature/database-schema to release/v1.3.0
63a08577965bc30fb492c2b7b80a45c7f101dcfc 8c587a313a7a06b961626470fef4336a186fabac ValentinoWang <<EMAIL>> 1754583532 +0800	commit: docs: merge comprehensive PRD documents from database-schema branch
8c587a313a7a06b961626470fef4336a186fabac b86829becc4363ea401f67c020ccccc8d74f6de4 ValentinoWang <<EMAIL>> 1754583689 +0800	checkout: moving from release/v1.3.0 to feature/performance
b86829becc4363ea401f67c020ccccc8d74f6de4 91482b30a7ad11096b4e835c606f5fef8a6665db ValentinoWang <<EMAIL>> 1754583702 +0800	merge origin/feature/documentation: Merge made by the 'ort' strategy.
91482b30a7ad11096b4e835c606f5fef8a6665db 8c587a313a7a06b961626470fef4336a186fabac ValentinoWang <<EMAIL>> 1754583966 +0800	checkout: moving from feature/performance to release/v1.3.0
8c587a313a7a06b961626470fef4336a186fabac eeeea195969627a43a0820264f7890d043ab581a ValentinoWang <<EMAIL>> 1754597562 +0800	commit: 测试严格通过
eeeea195969627a43a0820264f7890d043ab581a eeeea195969627a43a0820264f7890d043ab581a ValentinoWang <<EMAIL>> 1754597994 +0800	checkout: moving from release/v1.3.0 to feature/requirements-analysis
eeeea195969627a43a0820264f7890d043ab581a 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754598195 +0800	commit: feat(docs): complete requirements analysis for daily and cycle plans
8f1123aaa0486a4792f2c27da6b78e2035afc50b 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754598288 +0800	checkout: moving from feature/requirements-analysis to feature/data-architecture
8f1123aaa0486a4792f2c27da6b78e2035afc50b 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754598295 +0800	checkout: moving from feature/data-architecture to feature/api-architecture
8f1123aaa0486a4792f2c27da6b78e2035afc50b 1213cb0ba7b02d825b7d26270ac1451a896b759e ValentinoWang <<EMAIL>> 1754598663 +0800	commit: refactor: 优化分支策略，并行任务使用同一分支
1213cb0ba7b02d825b7d26270ac1451a896b759e 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754598665 +0800	checkout: moving from feature/api-architecture to feature/architecture
8f1123aaa0486a4792f2c27da6b78e2035afc50b 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754598736 +0800	checkout: moving from feature/architecture to feature/daily-plan-frontend
8f1123aaa0486a4792f2c27da6b78e2035afc50b 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754598742 +0800	checkout: moving from feature/daily-plan-frontend to feature/cycle-plan-frontend
8f1123aaa0486a4792f2c27da6b78e2035afc50b 1ee283d6ff633525c6c8b02670236b096fc6da1f ValentinoWang <<EMAIL>> 1754598989 +0800	commit: refactor: 优化分支策略，将并行任务统一到同一分支
1ee283d6ff633525c6c8b02670236b096fc6da1f 83d2d706bd215f876c793754152c358ac2a4c4c8 ValentinoWang <<EMAIL>> 1754599098 +0800	commit: refactor: 重新组织阶段编号，确保同阶段任务并行且同分支
83d2d706bd215f876c793754152c358ac2a4c4c8 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754599308 +0800	checkout: moving from feature/cycle-plan-frontend to feature/architecture
8f1123aaa0486a4792f2c27da6b78e2035afc50b 562986329d34f85bc6881f4b06ceb0631cb8a143 ValentinoWang <<EMAIL>> 1754600544 +0800	commit: feat(api): implement API endpoints for daily and cycle plans
562986329d34f85bc6881f4b06ceb0631cb8a143 6d9b8e3c845c1d780853317763ae36a62816f8ec ValentinoWang <<EMAIL>> 1754600609 +0800	commit: feat(db): implement data models for daily and cycle plans
6d9b8e3c845c1d780853317763ae36a62816f8ec f885899bf9860aa39d4b03d4621a5f5e149018a3 ValentinoWang <<EMAIL>> 1754600982 +0800	commit: feat(db): implement data models for daily and cycle plans
f885899bf9860aa39d4b03d4621a5f5e149018a3 67a40a05b9c21e95f47ae1d94add976a8b777292 ValentinoWang <<EMAIL>> 1754601064 +0800	checkout: moving from feature/architecture to feature/frontend
67a40a05b9c21e95f47ae1d94add976a8b777292 67a40a05b9c21e95f47ae1d94add976a8b777292 ValentinoWang <<EMAIL>> 1754601077 +0800	checkout: moving from feature/frontend to feature/frontend
67a40a05b9c21e95f47ae1d94add976a8b777292 6d9b8e3c845c1d780853317763ae36a62816f8ec ValentinoWang <<EMAIL>> 1754601094 +0800	pull origin feature/architecture: Fast-forward
6d9b8e3c845c1d780853317763ae36a62816f8ec 846b0036ab77ece38f27d3be547558c7462edc2e ValentinoWang <<EMAIL>> 1754601546 +0800	commit: feat(frontend): implement cycle plan configuration interface
846b0036ab77ece38f27d3be547558c7462edc2e 436064fde9f79400cbad5d5f509d6aa7f2aa77e9 ValentinoWang <<EMAIL>> 1754601803 +0800	commit: feat(frontend): implement daily plan configuration interface
436064fde9f79400cbad5d5f509d6aa7f2aa77e9 b551b0a3f7f91551c85d17ffdd976880d5ca32ca ValentinoWang <<EMAIL>> 1754601982 +0800	commit: feat(ui): optimize UI/UX for training plan configuration
b551b0a3f7f91551c85d17ffdd976880d5ca32ca 6d9b8e3c845c1d780853317763ae36a62816f8ec ValentinoWang <<EMAIL>> 1754602194 +0800	checkout: moving from feature/frontend to feature/backend
6d9b8e3c845c1d780853317763ae36a62816f8ec 6d9b8e3c845c1d780853317763ae36a62816f8ec ValentinoWang <<EMAIL>> 1754602205 +0800	checkout: moving from feature/backend to feature/backend
6d9b8e3c845c1d780853317763ae36a62816f8ec 132299bf81c95acb789879c8ee13b76f11129e63 ValentinoWang <<EMAIL>> 1754602550 +0800	commit: feat(backend): implement Phase 4.1 Daily Plan Service Development
132299bf81c95acb789879c8ee13b76f11129e63 311c0d8b8f16d484289772818a9d807b1b46e2af ValentinoWang <<EMAIL>> 1754602643 +0800	commit: feat(backend): implement comprehensive training analytics service
311c0d8b8f16d484289772818a9d807b1b46e2af dfb5544bcdd9b50a7735b81080a3fb4fd5b9943b ValentinoWang <<EMAIL>> 1754602785 +0800	commit: feat(backend): implement cycle plan service and repository
dfb5544bcdd9b50a7735b81080a3fb4fd5b9943b b551b0a3f7f91551c85d17ffdd976880d5ca32ca ValentinoWang <<EMAIL>> 1754602930 +0800	checkout: moving from feature/backend to feature/integration-testing
b551b0a3f7f91551c85d17ffdd976880d5ca32ca e95d4136542945085dcfa4814591ee9149bce2d5 ValentinoWang <<EMAIL>> 1754602933 +0800	merge origin/feature/backend: Merge made by the 'ort' strategy.
e95d4136542945085dcfa4814591ee9149bce2d5 c534feb5177bc2f12362f40a0a46de6a5d4dc1c6 ValentinoWang <<EMAIL>> 1754605632 +0800	commit: feat(testing): complete integration testing phase with dialog fixes
c534feb5177bc2f12362f40a0a46de6a5d4dc1c6 219dcd42bc20dc8f3c3eb93b3d6fbe3f95a29c4b ValentinoWang <<EMAIL>> 1754606604 +0800	commit: fix(testing): resolve save button conflicts in integration tests
219dcd42bc20dc8f3c3eb93b3d6fbe3f95a29c4b 1ec7f6be7731250209252b6854b9babe956b5e9d ValentinoWang <<EMAIL>> 1754606821 +0800	commit: fix(testing): resolve radio button and dialog issues in cycle config
1ec7f6be7731250209252b6854b9babe956b5e9d e77d7f525d90269cfd31142d7616a4d0c9a5913c ValentinoWang <<EMAIL>> 1754606872 +0800	commit: fix(testing): auto-set radio button values in cycle creation
e77d7f525d90269cfd31142d7616a4d0c9a5913c 948985a98c662e69ba8faae2210629211e3a7212 ValentinoWang <<EMAIL>> 1754606994 +0800	commit: fix(testing): resolve remaining UI interaction conflicts
948985a98c662e69ba8faae2210629211e3a7212 68c4486fc10f4b718eaeb0b644c3dfc0e653231e ValentinoWang <<EMAIL>> 1754607051 +0800	commit: fix(testing): resolve all sidebar text conflicts
68c4486fc10f4b718eaeb0b644c3dfc0e653231e 68c4486fc10f4b718eaeb0b644c3dfc0e653231e ValentinoWang <<EMAIL>> 1754627919 +0800	checkout: moving from feature/integration-testing to feature/optimization
68c4486fc10f4b718eaeb0b644c3dfc0e653231e 68c4486fc10f4b718eaeb0b644c3dfc0e653231e ValentinoWang <<EMAIL>> 1754627930 +0800	checkout: moving from feature/optimization to feature/optimization
68c4486fc10f4b718eaeb0b644c3dfc0e653231e 3e1562004d8205e8065b56aa69bdc032c4a50749 ValentinoWang <<EMAIL>> 1754629004 +0800	commit: docs: complete documentation for training plan module
3e1562004d8205e8065b56aa69bdc032c4a50749 703b14a7cb46e12294a581427bb54d91c45b982f ValentinoWang <<EMAIL>> 1754629342 +0800	commit: perf: optimize performance for training plan module
703b14a7cb46e12294a581427bb54d91c45b982f eeeea195969627a43a0820264f7890d043ab581a ValentinoWang <<EMAIL>> 1754629440 +0800	checkout: moving from feature/optimization to release/v1.3.0
eeeea195969627a43a0820264f7890d043ab581a ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ValentinoWang <<EMAIL>> 1754629460 +0800	merge origin/feature/optimization: Merge made by the 'ort' strategy.
ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ValentinoWang <<EMAIL>> 1754629507 +0800	checkout: moving from release/v1.3.0 to release/v1.3.2
ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 f885899bf9860aa39d4b03d4621a5f5e149018a3 ValentinoWang <<EMAIL>> 1754629710 +0800	checkout: moving from release/v1.3.2 to feature/architecture
f885899bf9860aa39d4b03d4621a5f5e149018a3 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754633183 +0800	checkout: moving from feature/architecture to feature/requirements-analysis
8f1123aaa0486a4792f2c27da6b78e2035afc50b 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754633211 +0800	reset: moving to HEAD
8f1123aaa0486a4792f2c27da6b78e2035afc50b ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ValentinoWang <<EMAIL>> 1754633214 +0800	checkout: moving from feature/requirements-analysis to release/v1.3.2
ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ValentinoWang <<EMAIL>> 1754633302 +0800	checkout: moving from release/v1.3.2 to backup-release-v1.3.2-20250808
ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ValentinoWang <<EMAIL>> 1754633313 +0800	checkout: moving from backup-release-v1.3.2-20250808 to release/v1.3.2
ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754633612 +0800	checkout: moving from release/v1.3.2 to feature/requirements-analysis
8f1123aaa0486a4792f2c27da6b78e2035afc50b 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754633654 +0800	reset: moving to HEAD
8f1123aaa0486a4792f2c27da6b78e2035afc50b ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ValentinoWang <<EMAIL>> 1754633658 +0800	checkout: moving from feature/requirements-analysis to release/v1.3.2
ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754633691 +0800	checkout: moving from release/v1.3.2 to feature/requirements-analysis
8f1123aaa0486a4792f2c27da6b78e2035afc50b 8f1123aaa0486a4792f2c27da6b78e2035afc50b ValentinoWang <<EMAIL>> 1754633896 +0800	reset: moving to HEAD
8f1123aaa0486a4792f2c27da6b78e2035afc50b ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 ValentinoWang <<EMAIL>> 1754633900 +0800	checkout: moving from feature/requirements-analysis to release/v1.3.2
ad85f9606ad6c0219cab3f0ae69d8041d9bdff13 b352ee5bb2079aa777c241ce3ad54e55824c096e ValentinoWang <<EMAIL>> 1754634075 +0800	commit: feat(types): add performance trends to dashboard stats and fix metric validation rules formatting
b352ee5bb2079aa777c241ce3ad54e55824c096e 6dac2ceb5742e4ddb55affc6e8fa475730bafc49 ValentinoWang <<EMAIL>> 1754634647 +0800	commit: release: prepare v1.3.2 - Training Plan Configuration Module
6dac2ceb5742e4ddb55affc6e8fa475730bafc49 79bdc3e037da80cec18042be68276089e3994d08 ValentinoWang <<EMAIL>> 1754636202 +0800	commit: fix(tests): comprehensive test fixes for v1.3.2 release
79bdc3e037da80cec18042be68276089e3994d08 4ff83466d0efc9f003634f61c283b92a92bfb110 ValentinoWang <<EMAIL>> 1754636263 +0800	commit: 修复一些问题
4ff83466d0efc9f003634f61c283b92a92bfb110 4ff83466d0efc9f003634f61c283b92a92bfb110 ValentinoWang <<EMAIL>> 1754637878 +0800	checkout: moving from release/v1.3.2 to fix/missing-schemas
4ff83466d0efc9f003634f61c283b92a92bfb110 f808e8a15c1af98f55f4c3cc2d45eba1c5ff786b ValentinoWang <<EMAIL>> 1754638072 +0800	commit: fix(schemas): add missing daily_plan.py and reorganize schemas structure
f808e8a15c1af98f55f4c3cc2d45eba1c5ff786b d9fa8797d21c87028875782ee1b00fbc182037b8 ValentinoWang <<EMAIL>> 1754638437 +0800	commit: fix(schemas): complete schema fixes with comprehensive DTOs and tests
d9fa8797d21c87028875782ee1b00fbc182037b8 4ff83466d0efc9f003634f61c283b92a92bfb110 ValentinoWang <<EMAIL>> 1754638478 +0800	checkout: moving from fix/missing-schemas to release/v1.3.2
4ff83466d0efc9f003634f61c283b92a92bfb110 d9fa8797d21c87028875782ee1b00fbc182037b8 ValentinoWang <<EMAIL>> 1754638484 +0800	merge fix/missing-schemas: Fast-forward
d9fa8797d21c87028875782ee1b00fbc182037b8 f885899bf9860aa39d4b03d4621a5f5e149018a3 ValentinoWang <<EMAIL>> 1754638499 +0800	checkout: moving from release/v1.3.2 to feature/architecture
f885899bf9860aa39d4b03d4621a5f5e149018a3 d9fa8797d21c87028875782ee1b00fbc182037b8 ValentinoWang <<EMAIL>> 1754638571 +0800	checkout: moving from feature/architecture to release/v1.3.2
d9fa8797d21c87028875782ee1b00fbc182037b8 d9fa8797d21c87028875782ee1b00fbc182037b8 ValentinoWang <<EMAIL>> 1754639996 +0800	checkout: moving from release/v1.3.2 to feature/1-data-models
d9fa8797d21c87028875782ee1b00fbc182037b8 d9fa8797d21c87028875782ee1b00fbc182037b8 ValentinoWang <<EMAIL>> 1754640012 +0800	checkout: moving from feature/1-data-models to feature/1-data-models
d9fa8797d21c87028875782ee1b00fbc182037b8 f293ed69955bc08a27a334cca3713746dfdccb53 ValentinoWang <<EMAIL>> 1754640601 +0800	commit: feat: Complete Stage 1 - Data Models Architecture
f293ed69955bc08a27a334cca3713746dfdccb53 f293ed69955bc08a27a334cca3713746dfdccb53 ValentinoWang <<EMAIL>> 1754640765 +0800	checkout: moving from feature/1-data-models to feature/2-shared-frontend
f293ed69955bc08a27a334cca3713746dfdccb53 f293ed69955bc08a27a334cca3713746dfdccb53 ValentinoWang <<EMAIL>> 1754640774 +0800	checkout: moving from feature/2-shared-frontend to feature/2-shared-frontend
f293ed69955bc08a27a334cca3713746dfdccb53 f293ed69955bc08a27a334cca3713746dfdccb53 ValentinoWang <<EMAIL>> 1754640815 +0800	reset: moving to HEAD
f293ed69955bc08a27a334cca3713746dfdccb53 f293ed69955bc08a27a334cca3713746dfdccb53 ValentinoWang <<EMAIL>> 1754640848 +0800	reset: moving to HEAD
f293ed69955bc08a27a334cca3713746dfdccb53 f293ed69955bc08a27a334cca3713746dfdccb53 ValentinoWang <<EMAIL>> 1754640926 +0800	reset: moving to HEAD
f293ed69955bc08a27a334cca3713746dfdccb53 185043ad49bf9b45857267da046f24350dca2e21 ValentinoWang <<EMAIL>> 1754641209 +0800	commit: feat: Complete LoadCurveChart with ECharts integration
185043ad49bf9b45857267da046f24350dca2e21 ceee141f68bbde87d13e8e53b11a7d7f465ad028 ValentinoWang <<EMAIL>> 1754641409 +0800	commit: feat: Implement ActionSelector Vue component with full functionality
ceee141f68bbde87d13e8e53b11a7d7f465ad028 dba74ee34e97baa33aba54f6fd372ca722e446ae ValentinoWang <<EMAIL>> 1754641442 +0800	commit: fix(MetricsSelector): Update store interface compatibility
dba74ee34e97baa33aba54f6fd372ca722e446ae 9dc99882aca6f86c0362dec4b0afc911028c419b ValentinoWang <<EMAIL>> 1754641917 +0800	commit: feat(tests): add comprehensive RestBlockEditor component tests
9dc99882aca6f86c0362dec4b0afc911028c419b 3b5bcc8edb32edf0b33667d0bee9c51d4502160c ValentinoWang <<EMAIL>> 1754642074 +0800	commit: feat(tests): add comprehensive ActionBlockEditor component tests
3b5bcc8edb32edf0b33667d0bee9c51d4502160c 3b5bcc8edb32edf0b33667d0bee9c51d4502160c ValentinoWang <<EMAIL>> 1754642263 +0800	reset: moving to HEAD
3b5bcc8edb32edf0b33667d0bee9c51d4502160c 882a1459ef641a298688f3fc1253f6dca6900de8 ValentinoWang <<EMAIL>> 1754642268 +0800	checkout: moving from feature/2-shared-frontend to develop/v1.3.0
882a1459ef641a298688f3fc1253f6dca6900de8 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754642276 +0800	pull origin develop/v1.3.0: Fast-forward
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754642282 +0800	checkout: moving from develop/v1.3.0 to feature/3-integration
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 f293ed69955bc08a27a334cca3713746dfdccb53 ValentinoWang <<EMAIL>> 1754642298 +0800	merge feature/1-data-models: Fast-forward
f293ed69955bc08a27a334cca3713746dfdccb53 3b5bcc8edb32edf0b33667d0bee9c51d4502160c ValentinoWang <<EMAIL>> 1754642321 +0800	merge feature/2-shared-frontend: Fast-forward
3b5bcc8edb32edf0b33667d0bee9c51d4502160c fc6f4ec8fbcc91445921746bb774dad7009682ca ValentinoWang <<EMAIL>> 1754644085 +0800	commit: feat: Complete integration testing for v1.3.2 training plan system
fc6f4ec8fbcc91445921746bb774dad7009682ca bec54b0ca0cca426031145047ca820795998b393 ValentinoWang <<EMAIL>> 1754645608 +0800	commit: fix(components): resolve critical rendering issues in CyclePlanConfig page
bec54b0ca0cca426031145047ca820795998b393 420e5f16bdd67a817e0c3be5c4fec056a8cc68e4 ValentinoWang <<EMAIL>> 1754653756 +0800	commit: 1.3.2测试完全通过
420e5f16bdd67a817e0c3be5c4fec056a8cc68e4 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754653936 +0800	checkout: moving from feature/3-integration to develop/v1.3.0
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 ValentinoWang <<EMAIL>> 1754653940 +0800	checkout: moving from develop/v1.3.0 to feature/4-advanced-features
cfc21e9ffc6d73a8a3f53ebf04eff74fcb511a85 420e5f16bdd67a817e0c3be5c4fec056a8cc68e4 ValentinoWang <<EMAIL>> 1754653951 +0800	merge feature/3-integration: Fast-forward
420e5f16bdd67a817e0c3be5c4fec056a8cc68e4 a9f08b2295b6967486f43efd573ef21a8760a273 ValentinoWang <<EMAIL>> 1754656642 +0800	commit: feat: Complete v1.3.2 Advanced Features Implementation
a9f08b2295b6967486f43efd573ef21a8760a273 a9f08b2295b6967486f43efd573ef21a8760a273 ValentinoWang <<EMAIL>> 1754656700 +0800	checkout: moving from feature/4-advanced-features to release/v1.3.3
a9f08b2295b6967486f43efd573ef21a8760a273 26cfb42aaa1b0118cfd084a3d11d1a779af9a455 ValentinoWang <<EMAIL>> 1754656974 +0800	commit: release: Prepare v1.3.3 - Advanced Intelligence Platform
26cfb42aaa1b0118cfd084a3d11d1a779af9a455 51aeda11a209a929e19067e8ee1d4c6ff279dbac ValentinoWang <<EMAIL>> 1754657066 +0800	commit: docs: Add release status report for v1.3.3
51aeda11a209a929e19067e8ee1d4c6ff279dbac 96d2476188c3adccd2bfd86e22f0ed19e983f717 ValentinoWang <<EMAIL>> 1754657234 +0800	commit: docs: Update README.md for v1.3.3 Advanced Intelligence Platform
96d2476188c3adccd2bfd86e22f0ed19e983f717 43c928c73b2d0d911237a6c2658a9e5b2cb661ff ValentinoWang <<EMAIL>> 1754660963 +0800	commit: feat: Add AI collaboration execution plan for version 1.3.2 and technical debt analysis report for version 1.3.3
43c928c73b2d0d911237a6c2658a9e5b2cb661ff 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754674142 +0800	commit: 开始更新到1.3.4前
8a086558e27476f7d4c76963083480d84f3053ce 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754674276 +0800	checkout: moving from release/v1.3.3 to main
8a086558e27476f7d4c76963083480d84f3053ce 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754674727 +0800	checkout: moving from main to feature/v1.3.4-1-backend-api
8a086558e27476f7d4c76963083480d84f3053ce f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754675256 +0800	commit: feat: 实现完整的后端API服务 (v1.3.4)
f0cb69a613fdbde8d8092413f569c8c7529ba922 f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754676190 +0800	checkout: moving from feature/v1.3.4-1-backend-api to feature/v1.3.4-2-core-functions
f0cb69a613fdbde8d8092413f569c8c7529ba922 f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754676318 +0800	reset: moving to HEAD
f0cb69a613fdbde8d8092413f569c8c7529ba922 f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754676389 +0800	reset: moving to HEAD
f0cb69a613fdbde8d8092413f569c8c7529ba922 d8e16ed2df54b9fdc776d464df4cca4b9cd444cd ValentinoWang <<EMAIL>> 1754676847 +0800	commit: feat: 实现动态表格生成功能 (v1.3.4-2)
d8e16ed2df54b9fdc776d464df4cca4b9cd444cd 0942157d130b976e963952fe3178b1cd6b4e4620 ValentinoWang <<EMAIL>> 1754676976 +0800	commit: fix: 完善动态表格组件依赖和清理代码
0942157d130b976e963952fe3178b1cd6b4e4620 7ab3697f918d6931721a73960a6ed579b037a039 ValentinoWang <<EMAIL>> 1754677130 +0800	commit: feat: 改进DynamicTrainingTable组件的TypeScript类型安全
7ab3697f918d6931721a73960a6ed579b037a039 0c6ebec3ea3fc9e9e861043537d40a20cc72bd35 ValentinoWang <<EMAIL>> 1754677189 +0800	commit: feat: 实现完整的模板保存功能 (v1.3.4-模板保存模块)
0c6ebec3ea3fc9e9e861043537d40a20cc72bd35 bc179eb3b488f740398407a724798be78831d89f ValentinoWang <<EMAIL>> 1754677325 +0800	commit: feat: 补充模板保存相关的后端和测试更新
bc179eb3b488f740398407a724798be78831d89f bc179eb3b488f740398407a724798be78831d89f ValentinoWang <<EMAIL>> 1754677643 +0800	checkout: moving from feature/v1.3.4-2-core-functions to feature/v1.3.4-3-ui-optimization
bc179eb3b488f740398407a724798be78831d89f be8f5ef868572d745b62b134f0effa5a6ad3aabe ValentinoWang <<EMAIL>> 1754678112 +0800	commit: feat(ui): Implement UI/UX optimizations for v1.3.4
be8f5ef868572d745b62b134f0effa5a6ad3aabe 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754678185 +0800	checkout: moving from feature/v1.3.4-3-ui-optimization to main
8a086558e27476f7d4c76963083480d84f3053ce 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754678191 +0800	checkout: moving from main to feature/v1.3.4-4-integration
8a086558e27476f7d4c76963083480d84f3053ce f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754678256 +0800	merge feature/v1.3.4-1-backend-api: Fast-forward
f0cb69a613fdbde8d8092413f569c8c7529ba922 bc179eb3b488f740398407a724798be78831d89f ValentinoWang <<EMAIL>> 1754678261 +0800	merge feature/v1.3.4-2-core-functions: Fast-forward
bc179eb3b488f740398407a724798be78831d89f be8f5ef868572d745b62b134f0effa5a6ad3aabe ValentinoWang <<EMAIL>> 1754678272 +0800	merge feature/v1.3.4-3-ui-optimization: Fast-forward
be8f5ef868572d745b62b134f0effa5a6ad3aabe f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754678425 +0800	checkout: moving from feature/v1.3.4-4-integration to feature/v1.3.4-1-backend-api
f0cb69a613fdbde8d8092413f569c8c7529ba922 be8f5ef868572d745b62b134f0effa5a6ad3aabe ValentinoWang <<EMAIL>> 1754678633 +0800	checkout: moving from feature/v1.3.4-1-backend-api to feature/v1.3.4-4-integration
be8f5ef868572d745b62b134f0effa5a6ad3aabe 77ab78414f1d2bad33a338d37a8e0403abf70ae6 ValentinoWang <<EMAIL>> 1754678657 +0800	commit: test(integration): Complete integration test suite for v1.3.4
77ab78414f1d2bad33a338d37a8e0403abf70ae6 f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754678662 +0800	checkout: moving from feature/v1.3.4-4-integration to feature/v1.3.4-1-backend-api
f0cb69a613fdbde8d8092413f569c8c7529ba922 bc179eb3b488f740398407a724798be78831d89f ValentinoWang <<EMAIL>> 1754678679 +0800	checkout: moving from feature/v1.3.4-1-backend-api to feature/v1.3.4-2-core-functions
bc179eb3b488f740398407a724798be78831d89f e48f861e33d15ece7443df04c29642fe6947c04f ValentinoWang <<EMAIL>> 1754679452 +0800	commit: feat: Initialize database module and implement user authentication and authorization models
e48f861e33d15ece7443df04c29642fe6947c04f 77ab78414f1d2bad33a338d37a8e0403abf70ae6 ValentinoWang <<EMAIL>> 1754679487 +0800	checkout: moving from feature/v1.3.4-2-core-functions to feature/v1.3.4-4-integration
77ab78414f1d2bad33a338d37a8e0403abf70ae6 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754679609 +0800	checkout: moving from feature/v1.3.4-4-integration to main
8a086558e27476f7d4c76963083480d84f3053ce 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754679620 +0800	checkout: moving from main to feature/v1.3.4-integration-fresh
8a086558e27476f7d4c76963083480d84f3053ce f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754679626 +0800	merge origin/feature/v1.3.4-1-backend-api: Fast-forward
f0cb69a613fdbde8d8092413f569c8c7529ba922 e48f861e33d15ece7443df04c29642fe6947c04f ValentinoWang <<EMAIL>> 1754679631 +0800	merge origin/feature/v1.3.4-2-core-functions: Fast-forward
e48f861e33d15ece7443df04c29642fe6947c04f d6d5b2fa72dfd3d4db99f294ca1e0f8559bc43c3 ValentinoWang <<EMAIL>> 1754679639 +0800	merge origin/feature/v1.3.4-3-ui-optimization: Merge made by the 'ort' strategy.
d6d5b2fa72dfd3d4db99f294ca1e0f8559bc43c3 902993aa053b21317238160efb6295e2aeee071a ValentinoWang <<EMAIL>> 1754679650 +0800	merge origin/feature/v1.3.4-4-integration: Merge made by the 'ort' strategy.
902993aa053b21317238160efb6295e2aeee071a e81640a8afb8ad9122ff4d70f3e95cd9e35d9332 ValentinoWang <<EMAIL>> 1754679794 +0800	commit: fix: Add pydantic compatibility for both v1 and v2
e81640a8afb8ad9122ff4d70f3e95cd9e35d9332 f0cb69a613fdbde8d8092413f569c8c7529ba922 ValentinoWang <<EMAIL>> 1754680435 +0800	checkout: moving from feature/v1.3.4-integration-fresh to feature/v1.3.4-1-backend-api
f0cb69a613fdbde8d8092413f569c8c7529ba922 be8f5ef868572d745b62b134f0effa5a6ad3aabe ValentinoWang <<EMAIL>> 1754680590 +0800	checkout: moving from feature/v1.3.4-1-backend-api to feature/v1.3.4-3-ui-optimization
be8f5ef868572d745b62b134f0effa5a6ad3aabe 77ab78414f1d2bad33a338d37a8e0403abf70ae6 ValentinoWang <<EMAIL>> 1754680599 +0800	checkout: moving from feature/v1.3.4-3-ui-optimization to feature/v1.3.4-4-integration
77ab78414f1d2bad33a338d37a8e0403abf70ae6 e48f861e33d15ece7443df04c29642fe6947c04f ValentinoWang <<EMAIL>> 1754680710 +0800	checkout: moving from feature/v1.3.4-4-integration to feature/v1.3.4-2-core-functions
e48f861e33d15ece7443df04c29642fe6947c04f be8f5ef868572d745b62b134f0effa5a6ad3aabe ValentinoWang <<EMAIL>> 1754680748 +0800	checkout: moving from feature/v1.3.4-2-core-functions to feature/v1.3.4-3-ui-optimization
be8f5ef868572d745b62b134f0effa5a6ad3aabe 77ab78414f1d2bad33a338d37a8e0403abf70ae6 ValentinoWang <<EMAIL>> 1754680759 +0800	checkout: moving from feature/v1.3.4-3-ui-optimization to feature/v1.3.4-4-integration
77ab78414f1d2bad33a338d37a8e0403abf70ae6 e48f861e33d15ece7443df04c29642fe6947c04f ValentinoWang <<EMAIL>> 1754680769 +0800	checkout: moving from feature/v1.3.4-4-integration to feature/v1.3.4-2-core-functions
e48f861e33d15ece7443df04c29642fe6947c04f f0518f3ccc5a7189fccd7edc64e6ece420445a1a ValentinoWang <<EMAIL>> 1754681217 +0800	commit: Add unified v1.3.4 test structure
f0518f3ccc5a7189fccd7edc64e6ece420445a1a 77ab78414f1d2bad33a338d37a8e0403abf70ae6 ValentinoWang <<EMAIL>> 1754681223 +0800	checkout: moving from feature/v1.3.4-2-core-functions to feature/v1.3.4-4-integration
77ab78414f1d2bad33a338d37a8e0403abf70ae6 da5f25df0c0ddfca419e1df7e2b611dd92f575c8 ValentinoWang <<EMAIL>> 1754681247 +0800	merge feature/v1.3.4-2-core-functions: Merge made by the 'ort' strategy.
da5f25df0c0ddfca419e1df7e2b611dd92f575c8 85d4b7db42f3d1c35b567f4e38b62c3c834557d4 ValentinoWang <<EMAIL>> 1754681310 +0800	commit: Clean up: Remove old scattered test files, keep unified v1.3.4 structure
85d4b7db42f3d1c35b567f4e38b62c3c834557d4 34cea8d4394a600b21da6e29ecf0618075769804 ValentinoWang <<EMAIL>> 1754722279 +0800	commit: feat: Complete E2E testing improvements for metric role assignment
34cea8d4394a600b21da6e29ecf0618075769804 cefbe21b8cf744f80d3b8979997379b0dde708f7 ValentinoWang <<EMAIL>> 1754723919 +0800	commit: feat: 完成E2E测试修复达到100%通过率
cefbe21b8cf744f80d3b8979997379b0dde708f7 6b52a49dca403d613c971be8c1e42d3500b888d5 ValentinoWang <<EMAIL>> 1754756148 +0800	commit: 测试改一半了
6b52a49dca403d613c971be8c1e42d3500b888d5 1a79a9870b63dbfa8e7fe2ff360db0c827dea695 ValentinoWang <<EMAIL>> 1754769660 +0800	commit: 1.3.4和1.1.0的一些测试
1a79a9870b63dbfa8e7fe2ff360db0c827dea695 b4d6323d6231ffffaa68dac00fd87ce2b165fc17 ValentinoWang <<EMAIL>> 1754803404 +0800	commit: 精细化修改
b4d6323d6231ffffaa68dac00fd87ce2b165fc17 6d89f875c14fd0c3641b562ef2c488d323b85e0b ValentinoWang <<EMAIL>> 1754807750 +0800	commit: 在将SQLite改成Postgresql之前的备份
6d89f875c14fd0c3641b562ef2c488d323b85e0b 0dc7c0e89d29ace352b9f8167798a62e440433b7 ValentinoWang <<EMAIL>> 1754808399 +0800	commit: feat: 配置Git LFS跟踪数据库文件
0dc7c0e89d29ace352b9f8167798a62e440433b7 7e45888467747d817e3bcff49955a8b917430897 ValentinoWang <<EMAIL>> 1754810294 +0800	commit: 在数据库开始备份前
7e45888467747d817e3bcff49955a8b917430897 7e45888467747d817e3bcff49955a8b917430897 ValentinoWang <<EMAIL>> 1754810360 +0800	checkout: moving from feature/v1.3.4-4-integration to feature/postgresql-migration
7e45888467747d817e3bcff49955a8b917430897 7e45888467747d817e3bcff49955a8b917430897 ValentinoWang <<EMAIL>> 1754811459 +0800	checkout: moving from feature/postgresql-migration to feature/postgresql-frontend-config
7e45888467747d817e3bcff49955a8b917430897 383daaa32941f0609304442e0599235429384ed7 ValentinoWang <<EMAIL>> 1754811553 +0800	commit: feat: PostgreSQL migration frontend config
383daaa32941f0609304442e0599235429384ed7 1111b557e6d26515893ff4debbb27ceba1f8409c ValentinoWang <<EMAIL>> 1754812854 +0800	commit: feat: 添加PostgreSQL迁移和验证脚本，优化模型定义
1111b557e6d26515893ff4debbb27ceba1f8409c a9b4e742db99f709c52cd3270f494d8a54042578 ValentinoWang <<EMAIL>> 1754812871 +0800	commit: feat: Implement PostgreSQL data import scripts and verification tools
a9b4e742db99f709c52cd3270f494d8a54042578 7e45888467747d817e3bcff49955a8b917430897 ValentinoWang <<EMAIL>> 1754812890 +0800	checkout: moving from feature/postgresql-frontend-config to feature/postgresql-migration
7e45888467747d817e3bcff49955a8b917430897 a9b4e742db99f709c52cd3270f494d8a54042578 ValentinoWang <<EMAIL>> 1754812982 +0800	checkout: moving from feature/postgresql-migration to feature/postgresql-frontend-config
a9b4e742db99f709c52cd3270f494d8a54042578 7e45888467747d817e3bcff49955a8b917430897 ValentinoWang <<EMAIL>> 1754812993 +0800	checkout: moving from feature/postgresql-frontend-config to feature/postgresql-migration
7e45888467747d817e3bcff49955a8b917430897 a9b4e742db99f709c52cd3270f494d8a54042578 ValentinoWang <<EMAIL>> 1754813001 +0800	merge feature/postgresql-frontend-config: Fast-forward
a9b4e742db99f709c52cd3270f494d8a54042578 54a092ecdfec9f0662c9da93927d7db1b333f8cd ValentinoWang <<EMAIL>> 1754813616 +0800	commit: feat: Complete PostgreSQL migration Phase 4 - Integration Testing
54a092ecdfec9f0662c9da93927d7db1b333f8cd b27bf667bfe193ddb609f00d1486c31da9df69e3 ValentinoWang <<EMAIL>> 1754814391 +0800	commit: feat: Complete PostgreSQL migration Phase 5 - Final cleanup and optimization
b27bf667bfe193ddb609f00d1486c31da9df69e3 9c6658096fceb14f5e147efe90409bb5531f47b5 ValentinoWang <<EMAIL>> 1754835352 +0800	commit: 指标重构前
9c6658096fceb14f5e147efe90409bb5531f47b5 e7322d3b82e1596b36a3b4c98035a52807c29833 ValentinoWang <<EMAIL>> 1754837558 +0800	commit: 同上
e7322d3b82e1596b36a3b4c98035a52807c29833 e7322d3b82e1596b36a3b4c98035a52807c29833 ValentinoWang <<EMAIL>> 1754837653 +0800	checkout: moving from feature/postgresql-migration to feature/v1.3.5-data-preparation
e7322d3b82e1596b36a3b4c98035a52807c29833 6f84597b0c106d694407707ac31f9ddff0e45cf5 ValentinoWang <<EMAIL>> 1754837939 +0800	commit: feat: 实现指标库重构数据分析系统
6f84597b0c106d694407707ac31f9ddff0e45cf5 e49fdd8510c7694d091c1331f7a9987b62532a1c ValentinoWang <<EMAIL>> 1754838276 +0800	commit: feat: 实现标准化CSV转换系统
e49fdd8510c7694d091c1331f7a9987b62532a1c 7b2d146a9ed3d41de0238553d024f5d7b7aad24c ValentinoWang <<EMAIL>> 1754839066 +0800	commit: feat: 实现PostgreSQL数据库重构，建立分层指标体系
7b2d146a9ed3d41de0238553d024f5d7b7aad24c 7b2d146a9ed3d41de0238553d024f5d7b7aad24c ValentinoWang <<EMAIL>> 1754839196 +0800	checkout: moving from feature/v1.3.5-data-preparation to feature/v1.3.5-template-config
7b2d146a9ed3d41de0238553d024f5d7b7aad24c 5e2e9d68947add1034cc9916daa969b3205a70bf ValentinoWang <<EMAIL>> 1754839494 +0800	commit: feat: implement running exercise template configuration system
5e2e9d68947add1034cc9916daa969b3205a70bf b46c25a42347df522bbf2aefec3ec422beb615cb ValentinoWang <<EMAIL>> 1754839686 +0800	commit: feat(template-config): 实现力量和技术类动作模板配置管理器
b46c25a42347df522bbf2aefec3ec422beb615cb 060634c96f725e40e9825f6aaca43ac9b9861a18 ValentinoWang <<EMAIL>> 1754840086 +0800	commit: feat: improve duplicate column handling in template configuration
060634c96f725e40e9825f6aaca43ac9b9861a18 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754840290 +0800	checkout: moving from feature/v1.3.5-template-config to main
8a086558e27476f7d4c76963083480d84f3053ce 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754840300 +0800	checkout: moving from main to feature/v1.3.5-integration
8a086558e27476f7d4c76963083480d84f3053ce 060634c96f725e40e9825f6aaca43ac9b9861a18 ValentinoWang <<EMAIL>> 1754840316 +0800	merge feature/v1.3.5-template-config: Fast-forward
060634c96f725e40e9825f6aaca43ac9b9861a18 39756d9c29145a6ff05cf3e97520a2e7b98e57d3 ValentinoWang <<EMAIL>> 1754841702 +0800	commit: feat: 完成v1.3.5集成测试和系统验证
39756d9c29145a6ff05cf3e97520a2e7b98e57d3 c737e78ff54efeedf839ad438ddce9db6bf376b0 ValentinoWang <<EMAIL>> 1754850720 +0800	commit: 测试全部通过，稳中求进
c737e78ff54efeedf839ad438ddce9db6bf376b0 939bdc0f0f6a47a89ccedc7bd4875ffa8d371435 ValentinoWang <<EMAIL>> 1754851504 +0800	commit: 如果运行不了就比较这部分和backend部分
939bdc0f0f6a47a89ccedc7bd4875ffa8d371435 fb804f11f5da8e54d19b21570acab6c7a395f711 ValentinoWang <<EMAIL>> 1754860387 +0800	commit: 演化进1.3.6前
fb804f11f5da8e54d19b21570acab6c7a395f711 fb804f11f5da8e54d19b21570acab6c7a395f711 ValentinoWang <<EMAIL>> 1754860433 +0800	checkout: moving from feature/v1.3.5-integration to feature/v1.3.6-preparation
fb804f11f5da8e54d19b21570acab6c7a395f711 4c31ac663dba2184ba65ecd6bff8c4a7cbe514b6 ValentinoWang <<EMAIL>> 1754860603 +0800	commit: feat: Phase 1 - 架构设计与文档更新
4c31ac663dba2184ba65ecd6bff8c4a7cbe514b6 4c31ac663dba2184ba65ecd6bff8c4a7cbe514b6 ValentinoWang <<EMAIL>> 1754860745 +0800	checkout: moving from feature/v1.3.6-preparation to feature/v1.3.6-component-refactor
4c31ac663dba2184ba65ecd6bff8c4a7cbe514b6 4c31ac663dba2184ba65ecd6bff8c4a7cbe514b6 ValentinoWang <<EMAIL>> 1754860770 +0800	checkout: moving from feature/v1.3.6-component-refactor to feature/v1.3.6-component-refactor
4c31ac663dba2184ba65ecd6bff8c4a7cbe514b6 fe401cadb4e0f03dbac043475412cb356161fc2f ValentinoWang <<EMAIL>> 1754861202 +0800	commit: feat(v1.3.6): 实现动作模板展示模块组件重构
fe401cadb4e0f03dbac043475412cb356161fc2f be7e97c0693cb893843c6122c67104786e72374c ValentinoWang <<EMAIL>> ********** +0800	commit: feat: A+B标签页架构完整实现 - v1.3.6组件重构
be7e97c0693cb893843c6122c67104786e72374c 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> ********** +0800	checkout: moving from feature/v1.3.6-component-refactor to main
8a086558e27476f7d4c76963083480d84f3053ce be7e97c0693cb893843c6122c67104786e72374c ValentinoWang <<EMAIL>> ********** +0800	checkout: moving from main to feature/v1.3.6-routing
be7e97c0693cb893843c6122c67104786e72374c 31046e3babab6e9b561589fda7ec1040dd19b470 ValentinoWang <<EMAIL>> ********** +0800	commit: feat: Complete v1.3.6 Phase 3 - Routing and navigation system adjustments
31046e3babab6e9b561589fda7ec1040dd19b470 31046e3babab6e9b561589fda7ec1040dd19b470 ValentinoWang <<EMAIL>> ********** +0800	checkout: moving from feature/v1.3.6-routing to feature/v1.3.6-api-state
31046e3babab6e9b561589fda7ec1040dd19b470 f2cd9341dd044be0e7aaa0ae79d8d87050c4673c ValentinoWang <<EMAIL>> ********** +0800	commit: feat(v1.3.6): 实现前端状态管理和API层重构
f2cd9341dd044be0e7aaa0ae79d8d87050c4673c f2cd9341dd044be0e7aaa0ae79d8d87050c4673c ValentinoWang <<EMAIL>> ********** +0800	reset: moving to HEAD
f2cd9341dd044be0e7aaa0ae79d8d87050c4673c ddb8b13c6b024d9557877ea468fc5b0de6544019 ValentinoWang <<EMAIL>> ********** +0800	commit: feat: Complete v1.3.6 Phase 4.1 - Backend API refactoring and state management
ddb8b13c6b024d9557877ea468fc5b0de6544019 ddb8b13c6b024d9557877ea468fc5b0de6544019 ValentinoWang <<EMAIL>> ********** +0800	checkout: moving from feature/v1.3.6-api-state to feature/v1.3.6-data-migration
ddb8b13c6b024d9557877ea468fc5b0de6544019 ac5f0f3f0454da313d67aef6f9ede536a91d999b ValentinoWang <<EMAIL>> ********** +0800	commit: feat(v1.3.6): 数据库迁移和模板配置系统重构
ac5f0f3f0454da313d67aef6f9ede536a91d999b d36bb126a4b8e8e0912e17f6877498102e68ff8f ValentinoWang <<EMAIL>> 1754864786 +0800	commit: feat: 添加自动化测试套件运行器
d36bb126a4b8e8e0912e17f6877498102e68ff8f cc700b07240696bb4a79e64c6fffdbb35343535f ValentinoWang <<EMAIL>> 1754864858 +0800	commit: fix: 修复Alembic迁移依赖链关系
cc700b07240696bb4a79e64c6fffdbb35343535f bd629866a77bd3fd1e1d6b7078dcc9abe3a123a8 ValentinoWang <<EMAIL>> 1754864991 +0800	commit: feat: 添加生产环境部署工具和文档
bd629866a77bd3fd1e1d6b7078dcc9abe3a123a8 b694bcc46ae99cd6a4ed6e32d5e9add69b1bed2c ValentinoWang <<EMAIL>> 1754865474 +0800	commit: fix: 完全修复Alembic迁移系统
b694bcc46ae99cd6a4ed6e32d5e9add69b1bed2c 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754865594 +0800	checkout: moving from feature/v1.3.6-data-migration to main
8a086558e27476f7d4c76963083480d84f3053ce 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754865609 +0800	checkout: moving from main to feature/v1.3.6-integration-test
8a086558e27476f7d4c76963083480d84f3053ce b694bcc46ae99cd6a4ed6e32d5e9add69b1bed2c ValentinoWang <<EMAIL>> 1754865623 +0800	merge feature/v1.3.6-data-migration: Fast-forward
b694bcc46ae99cd6a4ed6e32d5e9add69b1bed2c 61442632d92823c97e632d53a27895b191e5ff9a ValentinoWang <<EMAIL>> 1754867253 +0800	commit: feat(v1.3.6): 完成Phase 6集成测试和验证
61442632d92823c97e632d53a27895b191e5ff9a 97e1cd0bcb85146cfcd18b3bb183f4de8567c5f0 ValentinoWang <<EMAIL>> 1754879487 +0800	commit: 据说更改结束
97e1cd0bcb85146cfcd18b3bb183f4de8567c5f0 f9bc29e83a17d914981ad3fc84206c9f0ff64eeb ValentinoWang <<EMAIL>> 1754884900 +0800	commit: feat: 完成动作模板配置系统重构
f9bc29e83a17d914981ad3fc84206c9f0ff64eeb 0669246ede1d5d988d77f0a66236d98a8e63a6cd ValentinoWang <<EMAIL>> 1754889945 +0800	commit: refactor: 删除冗余的专项分类表
0669246ede1d5d988d77f0a66236d98a8e63a6cd e0c77dd8e4377f2851e86c6354a413c9cdc78fbc ValentinoWang <<EMAIL>> 1754931683 +0800	commit: 在新的风暴来临前
e0c77dd8e4377f2851e86c6354a413c9cdc78fbc e0c77dd8e4377f2851e86c6354a413c9cdc78fbc ValentinoWang <<EMAIL>> 1754933692 +0800	reset: moving to HEAD
e0c77dd8e4377f2851e86c6354a413c9cdc78fbc 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754933698 +0800	checkout: moving from feature/v1.3.6-integration-test to main
8a086558e27476f7d4c76963083480d84f3053ce 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1754933705 +0800	checkout: moving from main to feature/v1.3.6-tech-debt
8a086558e27476f7d4c76963083480d84f3053ce e0e608bf0c4d11396e03d0e7b0ef33bbf1a15560 ValentinoWang <<EMAIL>> 1754934685 +0800	commit: feat(v1.3.6): 完成Phase 0技术债务清理与准备
e0e608bf0c4d11396e03d0e7b0ef33bbf1a15560 e0e608bf0c4d11396e03d0e7b0ef33bbf1a15560 ValentinoWang <<EMAIL>> 1754935144 +0800	checkout: moving from feature/v1.3.6-tech-debt to feature/v1.3.6-db-migration
e0e608bf0c4d11396e03d0e7b0ef33bbf1a15560 6769a9115cc610050e92bee6892b966c590d0215 ValentinoWang <<EMAIL>> 1754936065 +0800	commit: feat(v1.3.6): Complete database structure migration
6769a9115cc610050e92bee6892b966c590d0215 330c3104858ce5d01d0f8ddddb32d89648e0ff88 ValentinoWang <<EMAIL>> 1754936637 +0800	commit: fix(v1.3.6): Fix migration ID references and add database state validation
330c3104858ce5d01d0f8ddddb32d89648e0ff88 da46f729095b096b2d46993ad1878540db525f38 ValentinoWang <<EMAIL>> 1754937047 +0800	commit: feat(v1.3.6): Successfully apply partial database migration with 75% completion
da46f729095b096b2d46993ad1878540db525f38 fca0ab94f33eb25617cb5de71e6cf2276bbd03c7 ValentinoWang <<EMAIL>> 1754937335 +0800	commit: feat(v1.3.6): 完成100%数据库架构迁移 - 生产就绪
fca0ab94f33eb25617cb5de71e6cf2276bbd03c7 bfce5b60e63228df2b576d82284744dbe004a988 ValentinoWang <<EMAIL>> 1754937579 +0800	commit: feat(database): 统一数据库对象所有权为track_user
bfce5b60e63228df2b576d82284744dbe004a988 c0d4cc287594b2a9ddc1dfc945943d882985033d ValentinoWang <<EMAIL>> 1754938141 +0800	commit: docs(test): 提供100%诚实的测试状态报告
c0d4cc287594b2a9ddc1dfc945943d882985033d b27f311890b4c8aee1a2d5dd5f9a9c73f6d2bcdc ValentinoWang <<EMAIL>> 1754944032 +0800	commit: 哈哈
b27f311890b4c8aee1a2d5dd5f9a9c73f6d2bcdc fcfa506b9fa887cd1003f5c50c28e2777b32e4f3 ValentinoWang <<EMAIL>> 1754944065 +0800	commit: 2通过
fcfa506b9fa887cd1003f5c50c28e2777b32e4f3 21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 ValentinoWang <<EMAIL>> 1754944076 +0800	commit: feat(report): 添加完整的诚实进展、测试结果和救赎报告，确保透明度和真实数据
21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 ValentinoWang <<EMAIL>> 1754944126 +0800	checkout: moving from feature/v1.3.6-db-migration to feature/v1.3.6-core-features
21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 ValentinoWang <<EMAIL>> 1754944195 +0800	checkout: moving from feature/v1.3.6-core-features to feature/v1.3.6-core-features
21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 ae19dffd6861395b246aa837bb67b6ed54dccffc ValentinoWang <<EMAIL>> 1754944924 +0800	commit: feat(v1.3.6): Complete Phase 2 - Daily Plan Composition Feature
ae19dffd6861395b246aa837bb67b6ed54dccffc ba8dc37ecc459ddc863100662bae257c5a220173 ValentinoWang <<EMAIL>> 1754945126 +0800	commit: feat(v1.3.6): 实现模板高级特性完整功能集
ba8dc37ecc459ddc863100662bae257c5a220173 ba8dc37ecc459ddc863100662bae257c5a220173 ValentinoWang <<EMAIL>> 1754945223 +0800	checkout: moving from feature/v1.3.6-core-features to feature/v1.3.6-cycle-config
ba8dc37ecc459ddc863100662bae257c5a220173 b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754946626 +0800	commit: feat(v1.3.6): Complete Phase 3 - Cycle Configuration Module Implementation
b41cdb11aeef58dfcec9233910b7fa514a49b52a b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947022 +0800	checkout: moving from feature/v1.3.6-cycle-config to feature/v1.3.6-test-migration
b41cdb11aeef58dfcec9233910b7fa514a49b52a b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947050 +0800	reset: moving to HEAD
b41cdb11aeef58dfcec9233910b7fa514a49b52a 21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 ValentinoWang <<EMAIL>> 1754947239 +0800	checkout: moving from feature/v1.3.6-test-migration to feature/v1.3.6-db-migration
21b26dc571bb9787eb76ab09fc2e5fbf1c6a7333 b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947556 +0800	checkout: moving from feature/v1.3.6-db-migration to feature/v1.3.6-cycle-config
b41cdb11aeef58dfcec9233910b7fa514a49b52a b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947755 +0800	checkout: moving from feature/v1.3.6-cycle-config to feature/v1.3.6-e2e-integration
b41cdb11aeef58dfcec9233910b7fa514a49b52a b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947764 +0800	checkout: moving from feature/v1.3.6-e2e-integration to feature/v1.3.6-performance
b41cdb11aeef58dfcec9233910b7fa514a49b52a b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947785 +0800	checkout: moving from feature/v1.3.6-performance to feature/v1.3.6-ui-interaction
b41cdb11aeef58dfcec9233910b7fa514a49b52a b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947804 +0800	checkout: moving from feature/v1.3.6-ui-interaction to feature/v1.3.6-db-performance
b41cdb11aeef58dfcec9233910b7fa514a49b52a b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754947811 +0800	checkout: moving from feature/v1.3.6-db-performance to feature/v1.3.6-business-logic
b41cdb11aeef58dfcec9233910b7fa514a49b52a 95a62501acc0c1226bef8392831ed8a2a8ce14d7 ValentinoWang <<EMAIL>> 1754948460 +0800	commit: feat(v1.3.6): Complete database performance test suite implementation
95a62501acc0c1226bef8392831ed8a2a8ce14d7 4e76beb1ea498de91865c6ebf7a749bab2134fe9 ValentinoWang <<EMAIL>> 1754948509 +0800	commit: feat(v1.3.6): Implement comprehensive business logic test suite
4e76beb1ea498de91865c6ebf7a749bab2134fe9 7fa41fed6629281d1f9aff2e03f123786939d438 ValentinoWang <<EMAIL>> 1754949040 +0800	commit: feat(v1.3.6): Complete E2E integration testing suite
7fa41fed6629281d1f9aff2e03f123786939d438 bb8aaeb3479786386c1c8a19ebcc335746b3fcc5 ValentinoWang <<EMAIL>> 1754949126 +0800	commit: feat(v1.3.6): Complete Performance Testing Engineer Role Implementation
bb8aaeb3479786386c1c8a19ebcc335746b3fcc5 bb8aaeb3479786386c1c8a19ebcc335746b3fcc5 ValentinoWang <<EMAIL>> 1754949153 +0800	reset: moving to HEAD
bb8aaeb3479786386c1c8a19ebcc335746b3fcc5 b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754949160 +0800	checkout: moving from feature/v1.3.6-business-logic to feature/v1.3.6-performance
b41cdb11aeef58dfcec9233910b7fa514a49b52a 5c15209c15a57ac0146f96d53d8cb91b782188fe ValentinoWang <<EMAIL>> 1754949331 +0800	commit: feat(v1.3.6): Complete Performance Testing Engineer Implementation
5c15209c15a57ac0146f96d53d8cb91b782188fe 7a2d218c4eb1da1527a259e80c27cb57e960862b ValentinoWang <<EMAIL>> 1754949616 +0800	commit: feat(v1.3.6): Complete E2E testing with results analysis
7a2d218c4eb1da1527a259e80c27cb57e960862b f30ff5aca1021a5135388725a898e8d3a4a19ff2 ValentinoWang <<EMAIL>> 1754969410 +0800	commit: docs(phase4): 完成Phase 4详细测试分配方案
f30ff5aca1021a5135388725a898e8d3a4a19ff2 b41cdb11aeef58dfcec9233910b7fa514a49b52a ValentinoWang <<EMAIL>> 1754969416 +0800	checkout: moving from feature/v1.3.6-performance to feature/v1.3.6-cycle-config
b41cdb11aeef58dfcec9233910b7fa514a49b52a f30ff5aca1021a5135388725a898e8d3a4a19ff2 ValentinoWang <<EMAIL>> 1754969428 +0800	merge feature/v1.3.6-performance: Fast-forward
f30ff5aca1021a5135388725a898e8d3a4a19ff2 f30ff5aca1021a5135388725a898e8d3a4a19ff2 ValentinoWang <<EMAIL>> 1754972418 +0800	checkout: moving from feature/v1.3.6-cycle-config to feature/v1.3.6-integration
f30ff5aca1021a5135388725a898e8d3a4a19ff2 f30ff5aca1021a5135388725a898e8d3a4a19ff2 ValentinoWang <<EMAIL>> 1754972436 +0800	checkout: moving from feature/v1.3.6-integration to feature/v1.3.6-cycle-config
f30ff5aca1021a5135388725a898e8d3a4a19ff2 88c219f4f44fa01179df96be1efea9f87b957605 ValentinoWang <<EMAIL>> 1754978789 +0800	commit: feat(v1.3.6): Complete Performance Testing Engineer B Implementation
88c219f4f44fa01179df96be1efea9f87b957605 892d364f369fa7f8a10b8052d4cd754b146f5375 ValentinoWang <<EMAIL>> 1754981176 +0800	commit: feat(v1.3.6): 添加Phase4并行测试执行方案和组件改进
892d364f369fa7f8a10b8052d4cd754b146f5375 892d364f369fa7f8a10b8052d4cd754b146f5375 ValentinoWang <<EMAIL>> 1754984467 +0800	reset: moving to HEAD
892d364f369fa7f8a10b8052d4cd754b146f5375 f17809d992b73b6c64a9fb32acb27efc2303c4f8 ValentinoWang <<EMAIL>> 1755006685 +0800	commit: save current work before creating fix branch
f17809d992b73b6c64a9fb32acb27efc2303c4f8 f17809d992b73b6c64a9fb32acb27efc2303c4f8 ValentinoWang <<EMAIL>> 1755006690 +0800	checkout: moving from feature/v1.3.6-cycle-config to fix/v1.3.6-api-errors
f17809d992b73b6c64a9fb32acb27efc2303c4f8 f96da3e9901438154c919466ea2be621c2abf00c ValentinoWang <<EMAIL>> 1755007604 +0800	commit: fix(v1.3.6): Fix API 500 errors - database model mapping
f96da3e9901438154c919466ea2be621c2abf00c f96da3e9901438154c919466ea2be621c2abf00c ValentinoWang <<EMAIL>> 1755008563 +0800	checkout: moving from fix/v1.3.6-api-errors to feature/v1.3.6-enhancement
f96da3e9901438154c919466ea2be621c2abf00c f96da3e9901438154c919466ea2be621c2abf00c ValentinoWang <<EMAIL>> 1755008573 +0800	checkout: moving from feature/v1.3.6-enhancement to feature/v1.3.6-enhancement
f96da3e9901438154c919466ea2be621c2abf00c 3a9a83acef946f447302b7343c105c72664e8a06 ValentinoWang <<EMAIL>> 1755008603 +0800	pull --rebase (start): checkout 3a9a83acef946f447302b7343c105c72664e8a06
3a9a83acef946f447302b7343c105c72664e8a06 181e89be14fdbf6dfa690ae9f97cda8d3e45bfba ValentinoWang <<EMAIL>> 1755008603 +0800	pull --rebase (pick): feat(v1.3.6): Complete Performance Testing Engineer B Implementation
181e89be14fdbf6dfa690ae9f97cda8d3e45bfba 7d4d70323aa36cfde813833d3b52c1a12a48287b ValentinoWang <<EMAIL>> 1755008603 +0800	pull --rebase (pick): feat(v1.3.6): 添加Phase4并行测试执行方案和组件改进
7d4d70323aa36cfde813833d3b52c1a12a48287b fcb3722595b049c6a404e7b51d200a371daf84b7 ValentinoWang <<EMAIL>> 1755008881 +0800	rebase (continue): save current work before creating fix branch
fcb3722595b049c6a404e7b51d200a371daf84b7 0dc8bb1d0a6eaea151112df3471a512a5c5fe2b2 ValentinoWang <<EMAIL>> 1755008881 +0800	rebase (pick): fix(v1.3.6): Fix API 500 errors - database model mapping
0dc8bb1d0a6eaea151112df3471a512a5c5fe2b2 0dc8bb1d0a6eaea151112df3471a512a5c5fe2b2 ValentinoWang <<EMAIL>> 1755008881 +0800	rebase (finish): returning to refs/heads/feature/v1.3.6-enhancement
0dc8bb1d0a6eaea151112df3471a512a5c5fe2b2 ed5fce944edd75784319f9a1c17c56db67d21cfb ValentinoWang <<EMAIL>> 1755008911 +0800	commit: feat(v1.3.6): 完成MetricRoleAssigner组件中性指标配置功能
ed5fce944edd75784319f9a1c17c56db67d21cfb af0f95a8c588eaa881f152dd2e93fd27086678f0 ValentinoWang <<EMAIL>> 1755009457 +0800	commit: feat(v1.3.6): 完成数据流集成工程师实现
af0f95a8c588eaa881f152dd2e93fd27086678f0 4782102ae5bb13ce13ae9eb2e18ed5ad8d12aeea ValentinoWang <<EMAIL>> 1755009688 +0800	commit: feat(v1.3.6): Complete frontend data flow engineering tasks
4782102ae5bb13ce13ae9eb2e18ed5ad8d12aeea 4782102ae5bb13ce13ae9eb2e18ed5ad8d12aeea ValentinoWang <<EMAIL>> 1755009809 +0800	checkout: moving from feature/v1.3.6-enhancement to feature/v1.3.6-enhancement
4782102ae5bb13ce13ae9eb2e18ed5ad8d12aeea 19a4205288e7ea303febb8257b24e62893ad439d ValentinoWang <<EMAIL>> 1755010602 +0800	commit: fix: Address audit findings and restore project integrity
19a4205288e7ea303febb8257b24e62893ad439d 7df272477560818d3e2dcca7bf2931f4a7cc401d ValentinoWang <<EMAIL>> 1755010697 +0800	commit: docs: Add comprehensive audit response action plan
7df272477560818d3e2dcca7bf2931f4a7cc401d 9f792900db3b7fe45270e44db3137aee401d2698 ValentinoWang <<EMAIL>> 1755011723 +0800	commit: feat: Complete audit response with comprehensive rebuttal
9f792900db3b7fe45270e44db3137aee401d2698 e9da4c2aacb48f6f26c23ad2f7a485e5103bc1c6 ValentinoWang <<EMAIL>> 1755013226 +0800	commit: fix: Complete cleanup of deceptive code and begin honest implementation
e9da4c2aacb48f6f26c23ad2f7a485e5103bc1c6 e9da4c2aacb48f6f26c23ad2f7a485e5103bc1c6 ValentinoWang <<EMAIL>> 1755021042 +0800	reset: moving to HEAD
e9da4c2aacb48f6f26c23ad2f7a485e5103bc1c6 f17809d992b73b6c64a9fb32acb27efc2303c4f8 ValentinoWang <<EMAIL>> 1755021047 +0800	checkout: moving from feature/v1.3.6-enhancement to feature/v1.3.6-cycle-config
f17809d992b73b6c64a9fb32acb27efc2303c4f8 964ddc146154336b1641d6a3ea7eb17620e7cf98 ValentinoWang <<EMAIL>> 1755021361 +0800	commit: feat(ui): 完善MetricRoleAssigner拖拽和批量操作功能
964ddc146154336b1641d6a3ea7eb17620e7cf98 4c262ea0504225baf231896c95ce4188e2411043 ValentinoWang <<EMAIL>> 1755022092 +0800	commit: fix: 清理MetricRoleAssigner调试代码和未使用变量
4c262ea0504225baf231896c95ce4188e2411043 3267f6c86211e90d5a19ebd8b83abb0e600d734e ValentinoWang <<EMAIL>> 1755022576 +0800	commit: fix: 恢复被错误删除的showRoleAssignmentMenu变量
3267f6c86211e90d5a19ebd8b83abb0e600d734e 8772679f19f2a23f4ba66e694ebed89eec8f4156 ValentinoWang <<EMAIL>> 1755022978 +0800	commit: fix: 添加缺失的QuestionFilled图标导入
8772679f19f2a23f4ba66e694ebed89eec8f4156 50de44eac215e192eb82637eaab49445f2542a18 ValentinoWang <<EMAIL>> 1755023130 +0800	commit: fix: Address audit findings with honest implementation and real tests
50de44eac215e192eb82637eaab49445f2542a18 8a3b5ac6b6ba558304704deb279f916035d4a094 ValentinoWang <<EMAIL>> 1755051828 +0800	commit: 完成审计问题修复和导入路径标准化
8a3b5ac6b6ba558304704deb279f916035d4a094 8a3b5ac6b6ba558304704deb279f916035d4a094 ValentinoWang <<EMAIL>> 1755051856 +0800	checkout: moving from feature/v1.3.6-cycle-config to test/integration-v1.3.6
8a3b5ac6b6ba558304704deb279f916035d4a094 df1805cdfa09cb6a51dfc6169eb28fd037d3d516 ValentinoWang <<EMAIL>> 1755052352 +0800	commit (merge): 集成合并: 解决feature/v1.3.6-enhancement分支冲突
df1805cdfa09cb6a51dfc6169eb28fd037d3d516 6c011174c41e9a321f3dbeb10f2f651a83ee0758 ValentinoWang <<EMAIL>> 1755052531 +0800	commit: docs: 添加V1.3.6集成冲突解决报告
6c011174c41e9a321f3dbeb10f2f651a83ee0758 c8cee36b7e32524bd8245eacc5f4904240b5a269 ValentinoWang <<EMAIL>> 1755065261 +0800	commit: 角色偏离纠正: 提交E2E测试员期间误开发的前端功能
c8cee36b7e32524bd8245eacc5f4904240b5a269 c8cee36b7e32524bd8245eacc5f4904240b5a269 ValentinoWang <<EMAIL>> 1755108600 +0800	checkout: moving from test/integration-v1.3.6 to feature/v1.3.6-frontend-testing
c8cee36b7e32524bd8245eacc5f4904240b5a269 c8cee36b7e32524bd8245eacc5f4904240b5a269 ValentinoWang <<EMAIL>> 1755108620 +0800	checkout: moving from feature/v1.3.6-frontend-testing to test/integration-v1.3.6
c8cee36b7e32524bd8245eacc5f4904240b5a269 c8cee36b7e32524bd8245eacc5f4904240b5a269 ValentinoWang <<EMAIL>> 1755145465 +0800	checkout: moving from test/integration-v1.3.6 to fix/v1.3.6-critical-bugs
c8cee36b7e32524bd8245eacc5f4904240b5a269 4c7b381606f45fb8193d19221bf00a4ffd293884 ValentinoWang <<EMAIL>> 1755146606 +0800	commit: fix(P0): 修复关键基础设施问题
4c7b381606f45fb8193d19221bf00a4ffd293884 4c7b381606f45fb8193d19221bf00a4ffd293884 ValentinoWang <<EMAIL>> 1755146715 +0800	checkout: moving from fix/v1.3.6-critical-bugs to feature/v1.3.6-ui-enhancement
4c7b381606f45fb8193d19221bf00a4ffd293884 4c7b381606f45fb8193d19221bf00a4ffd293884 ValentinoWang <<EMAIL>> 1755146744 +0800	checkout: moving from feature/v1.3.6-ui-enhancement to fix/v1.3.6-critical-bugs
4c7b381606f45fb8193d19221bf00a4ffd293884 50d5734dc801d9821db25cd10471f2d5faf213b6 ValentinoWang <<EMAIL>> 1755185264 +0800	commit: 重要的修改
50d5734dc801d9821db25cd10471f2d5faf213b6 8a086558e27476f7d4c76963083480d84f3053ce ValentinoWang <<EMAIL>> 1755185522 +0800	checkout: moving from fix/v1.3.6-critical-bugs to main
8a086558e27476f7d4c76963083480d84f3053ce be4b029aa4f576a943f292eb80163dadf55ce04a ValentinoWang <<EMAIL>> 1755185533 +0800	pull origin main: Fast-forward
be4b029aa4f576a943f292eb80163dadf55ce04a 86225c836f4c911eb8345d3772fc9d2864da860b ValentinoWang <<EMAIL>> 1755185934 +0800	commit: 1.3.6发布
86225c836f4c911eb8345d3772fc9d2864da860b 4a52e01e24857ca9aca53b61ea5769e168719672 ValentinoWang <<EMAIL>> 1755188660 +0800	commit: 1.3.6版本整理完成
4a52e01e24857ca9aca53b61ea5769e168719672 2ee1f797455bff8fff798cfbf0070d691b30339b ValentinoWang <<EMAIL>> 1755224217 +0800	commit: 小小同步一下
2ee1f797455bff8fff798cfbf0070d691b30339b da7563ee9d58215e54e4e8c52d0175f015d81b9c ValentinoWang <<EMAIL>> 1755224235 +0800	commit: 同上
da7563ee9d58215e54e4e8c52d0175f015d81b9c 7fe8f0aa5a1339a0f7c39bf4c16e59976cbbe329 ValentinoWang <<EMAIL>> 1755329034 +0800	commit: 1.3.6改进中
7fe8f0aa5a1339a0f7c39bf4c16e59976cbbe329 52934491a79f606fc94569d803bf8dbe6df749ce ValentinoWang <<EMAIL>> 1755456327 +0800	commit: 删除TemplateBuilder.vue和TemplateGenerator.vue /MetricsLibrary.vue重构前
52934491a79f606fc94569d803bf8dbe6df749ce 521257fddd8e7530dff64a4633e79be8a04017b1 ValentinoWang <<EMAIL>> 1755487566 +0800	commit: refactor: 完成MetricsLibrary.vue组件化重构
521257fddd8e7530dff64a4633e79be8a04017b1 b652e9f5c42a3c632db6ac6aef4d80c845766674 ValentinoWang <<EMAIL>> 1755488456 +0800	commit: fix: 修复MetricsLibrary表格配色问题
b652e9f5c42a3c632db6ac6aef4d80c845766674 ea943c3a43b31ff450bd739536e0555a790d9b1b ValentinoWang <<EMAIL>> 1755488961 +0800	commit: fix: 修复快速操作区域按钮样式
ea943c3a43b31ff450bd739536e0555a790d9b1b a4f5180d0ec8de9c84982a82486cfde6929709eb ValentinoWang <<EMAIL>> 1755495331 +0800	commit: fix: 强化快速操作按钮样式覆盖优先级
a4f5180d0ec8de9c84982a82486cfde6929709eb 1cae2a3c17c7ccd399e386540c8ee83870fc78ff ValentinoWang <<EMAIL>> 1755526873 +0800	commit: 删除MetricDialog.vue保留MetricDialogEnhanced.vue 前
1cae2a3c17c7ccd399e386540c8ee83870fc78ff 8a6cf2c81559f7970312072697bb11cf29d5e9b4 ValentinoWang <<EMAIL>> 1755531266 +0800	commit: 完全移除 is_neutral前
8a6cf2c81559f7970312072697bb11cf29d5e9b4 7ec20d148c131fd3764c0e2365b83089b8622b84 ValentinoWang <<EMAIL>> 1755550812 +0800	commit: 数据库处理前
7ec20d148c131fd3764c0e2365b83089b8622b84 5de0230fccd755ea02e8cb195ae3c5b7a2d6b200 ValentinoWang <<EMAIL>> 1755603519 +0800	commit: 部分完成
5de0230fccd755ea02e8cb195ae3c5b7a2d6b200 28d8893a4fb9d108bd2b2b05d7834d626a4fa537 ValentinoWang <<EMAIL>> 1755640562 +0800	commit: 头发处理前
28d8893a4fb9d108bd2b2b05d7834d626a4fa537 b866c7fbd9c0bd513adb0ea45929e4075d6174f4 ValentinoWang <<EMAIL>> 1755650903 +0800	commit: 业务架构分离改造前
b866c7fbd9c0bd513adb0ea45929e4075d6174f4 508cb38a3eb80e21ae3b14a522a22275a82fe064 ValentinoWang <<EMAIL>> 1755856606 +0800	commit: 改了不少
508cb38a3eb80e21ae3b14a522a22275a82fe064 d6b2e0cdc5fc6dd79deba11d0fdf9e51d4a3971d ValentinoWang <<EMAIL>> 1755856626 +0800	commit: Add comprehensive tests for CSV metrics analysis, visual explanation of database relationships, and Vue page usage analysis
d6b2e0cdc5fc6dd79deba11d0fdf9e51d4a3971d 488f1850c3962242e5d3633c3e6b9f8f9726b1d4 ValentinoWang <<EMAIL>> 1755919728 +0800	commit: 将ExerciseTemplateBuilder.vue进行拆解前
488f1850c3962242e5d3633c3e6b9f8f9726b1d4 14943ff9baaf8c62ad954fe4c50fbb7abe0b20c1 ValentinoWang <<EMAIL>> 1756050529 +0800	commit: 配置Git LFS支持数据库文件管理
14943ff9baaf8c62ad954fe4c50fbb7abe0b20c1 fe73c133f9c0b1014c64ddc3ab2fbbfc81800070 ValentinoWang <<EMAIL>> 1756052810 +0800	commit: 整理文件后
fe73c133f9c0b1014c64ddc3ab2fbbfc81800070 a2d81cf3b23086dda60b12134187eaac6a4cb3ad ValentinoWang <<EMAIL>> 1756053066 +0800	commit: 同上
a2d81cf3b23086dda60b12134187eaac6a4cb3ad a2d81cf3b23086dda60b12134187eaac6a4cb3ad ValentinoWang <<EMAIL>> 1756053402 +0800	checkout: moving from main to release/v1.3.6
a2d81cf3b23086dda60b12134187eaac6a4cb3ad 07e53f904c489b245558252c7a81188ecc0f1979 ValentinoWang <<EMAIL>> 1756053600 +0800	commit: 配置Git LFS支持媒体文件和数据库文件管理
07e53f904c489b245558252c7a81188ecc0f1979 b77f3105625a3ebc1618f98ef1baa9020d92a68b ValentinoWang <<EMAIL>> 1756214722 +0800	commit: CyclePlanBuilder.vue 重构后
b77f3105625a3ebc1618f98ef1baa9020d92a68b c5675792e0cff010d76b0a7f51948ec050d39650 ValentinoWang <<EMAIL>> 1756275006 +0800	commit: feat: Prepare release v1.3.6 with complete CI/CD pipeline and deployment scripts
c5675792e0cff010d76b0a7f51948ec050d39650 62ad51eb1b768c0d393fdf23701789d21818a226 ValentinoWang <<EMAIL>> 1756275124 +0800	commit: fix: Use virtual environment Python for tests in release script
62ad51eb1b768c0d393fdf23701789d21818a226 a4f98986c21e172f6bf35763e2810dcb934c7cc8 ValentinoWang <<EMAIL>> 1756275190 +0800	commit: fix: Allow release to continue if tests fail (non-blocking)
a4f98986c21e172f6bf35763e2810dcb934c7cc8 b914261564e1ab753a2014e0476d7510c268213c ValentinoWang <<EMAIL>> 1756275672 +0800	commit: docs: Add comprehensive user guides for v1.3.6 release
b914261564e1ab753a2014e0476d7510c268213c 2452595be57d0de957c5a6cdec9a3b5d42e2663c ValentinoWang <<EMAIL>> 1756275747 +0800	commit: fix: 修复备份目录路径并更新健康检查脚本权限，优化发布脚本中的前端构建命令
2452595be57d0de957c5a6cdec9a3b5d42e2663c 1cbc9cfae7270b1d293ab7f49358daec7c58ec88 ValentinoWang <<EMAIL>> 1756278045 +0800	commit: feat: Complete Docker data sharing system for PostgreSQL
1cbc9cfae7270b1d293ab7f49358daec7c58ec88 b4fc19ef34e00a45b1fc814fa925a5803e10f019 ValentinoWang <<EMAIL>> 1756283558 +0800	commit: release1.3..6 wth data
b4fc19ef34e00a45b1fc814fa925a5803e10f019 b4fc19ef34e00a45b1fc814fa925a5803e10f019 XieZhiliang <<EMAIL>> 1756372127 +0800	checkout: moving from release/v1.3.6 to XieZhiliang
b4fc19ef34e00a45b1fc814fa925a5803e10f019 b4fc19ef34e00a45b1fc814fa925a5803e10f019 XieZhiliang <<EMAIL>> 1756379032 +0800	checkout: moving from XieZhiliang to XieZhiliang
b4fc19ef34e00a45b1fc814fa925a5803e10f019 b4fc19ef34e00a45b1fc814fa925a5803e10f019 XieZhiliang <<EMAIL>> 1756379045 +0800	checkout: moving from XieZhiliang to XieZhiliang
b4fc19ef34e00a45b1fc814fa925a5803e10f019 e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756379091 +0800	commit: 下载了node.js等文件使得项目能够适配windows电脑 但是还是不能查看数据
e26ace687861c0fe3c39afd5abbfe3345c7d97eb e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756379272 +0800	checkout: moving from XieZhiliang to XieZhiliang
e26ace687861c0fe3c39afd5abbfe3345c7d97eb e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756381556 +0800	checkout: moving from XieZhiliang to XieZhiliang
e26ace687861c0fe3c39afd5abbfe3345c7d97eb b4fc19ef34e00a45b1fc814fa925a5803e10f019 XieZhiliang <<EMAIL>> 1756381747 +0800	checkout: moving from XieZhiliang to release/v1.3.6
b4fc19ef34e00a45b1fc814fa925a5803e10f019 b4fc19ef34e00a45b1fc814fa925a5803e10f019 XieZhiliang <<EMAIL>> 1756382136 +0800	reset: moving to HEAD
b4fc19ef34e00a45b1fc814fa925a5803e10f019 b4fc19ef34e00a45b1fc814fa925a5803e10f019 XieZhiliang <<EMAIL>> 1756382166 +0800	reset: moving to HEAD
b4fc19ef34e00a45b1fc814fa925a5803e10f019 d527da611e4bd8e99f3dabfb321071eb13773fd2 XieZhiliang <<EMAIL>> 1756382954 +0800	commit: Update file permissions for various scripts and migration files to ensure proper execution rights
d527da611e4bd8e99f3dabfb321071eb13773fd2 e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756382968 +0800	checkout: moving from release/v1.3.6 to XieZhiliang
e26ace687861c0fe3c39afd5abbfe3345c7d97eb e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756383130 +0800	checkout: moving from XieZhiliang to XieZhiliang
e26ace687861c0fe3c39afd5abbfe3345c7d97eb e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756383262 +0800	checkout: moving from XieZhiliang to XieZhiliang
e26ace687861c0fe3c39afd5abbfe3345c7d97eb e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756383369 +0800	checkout: moving from XieZhiliang to XieZhiliang
e26ace687861c0fe3c39afd5abbfe3345c7d97eb e26ace687861c0fe3c39afd5abbfe3345c7d97eb XieZhiliang <<EMAIL>> 1756383379 +0800	checkout: moving from XieZhiliang to XieZhiliang
